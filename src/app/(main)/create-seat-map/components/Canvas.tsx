"use client";
import ZoomLevel from "./ZoomLevel";
import { useSeatMapContext } from "../context";
import useSeatMapStore from "@stores/useSeatMapStore";
import LayoutInfo from "./LayoutInfo";
import useDimensionsListener from "../hooks/useDimensionsListener";
import { useEffect, useRef } from "react";
import { SeatMapEngine } from "../lib/SeatMapEngine";
import { SeatMapEvent } from "../lib/types";

export default function Canvas() {
  useDimensionsListener();
  const { stageRef } = useSeatMapContext();
  const { stageDimensions } = useSeatMapStore();
  const canvasRef = useRef<HTMLDivElement>(null);
  const seatMapEngineRef = useRef<SeatMapEngine | null>(null);

  // Initialize the seat map engine
  useEffect(() => {
    if (!canvasRef.current || seatMapEngineRef.current) return;

    try {
      const engine = new SeatMapEngine(canvasRef.current, {
        enableVirtualization: true,
        enableCaching: true,
        maxSeatsPerFrame: 1000
      });

      // Add event listeners
      engine.addEventListener((event: SeatMapEvent) => {
        console.log('Seat map event:', event);
      });

      seatMapEngineRef.current = engine;

      // Resize handler
      const handleResize = () => {
        if (engine && canvasRef.current) {
          engine.resize(canvasRef.current.clientWidth, canvasRef.current.clientHeight);
        }
      };

      window.addEventListener('resize', handleResize);

      return () => {
        window.removeEventListener('resize', handleResize);
        engine.destroy();
      };
    } catch (error) {
      console.error('Failed to initialize seat map engine:', error);
    }
  }, []);

  // Update engine dimensions when stage dimensions change
  useEffect(() => {
    if (seatMapEngineRef.current && stageDimensions.w > 0 && stageDimensions.h > 0) {
      seatMapEngineRef.current.resize(stageDimensions.w, stageDimensions.h);
    }
  }, [stageDimensions]);

  // Expose engine to context for other components to use
  useEffect(() => {
    if (stageRef && seatMapEngineRef.current) {
      // Store engine reference in the stage ref for access by other components
      (stageRef as any).seatMapEngine = seatMapEngineRef.current;
    }
  }, [stageRef]);

  return (
    <div className="bg-gray-light100 relative flex h-[calc(100vh-72px)] w-full items-center justify-center">
      <ZoomLevel stageScale={1} />
      <LayoutInfo />
      <div
        ref={canvasRef}
        className="border-l border-gray-200 w-full h-full"
        style={{ width: stageDimensions.w, height: stageDimensions.h }}
      />
    </div>
  );
}
