"use client";
import { SidePanelHeader } from "@components/builder/PanelHeader";
import { useSeatMapContext } from "../context";
import AddItemButton from "./AddItemButton";
import DotsCircleIcon from "mdi-react/DotsCircleIcon";
import AccountGroupIcon from "mdi-react/AccountGroupIcon";
import VectorRectangleIcon from "mdi-react/VectorRectangleIcon";
import DotsSquareIcon from "mdi-react/DotsSquareIcon";
import SignDirectionIcon from "mdi-react/SignDirectionIcon";
import NameForm from "./NameForm";
import useSeatMapStore from "@stores/useSeatMapStore";
import PerformanceDemo from "./PerformanceDemo";

export default function MainPanel() {
  const { setNewItemView } = useSeatMapStore();
  const { stageRef, layerRef } = useSeatMapContext();

  return (
    <div className="absolute left-0 top-0 z-0 flex h-full w-[400px] flex-shrink-0 flex-col border-gray-200 bg-white">
      <SidePanelHeader title={"Your Seat Map"} />
      <div className="flex flex-col gap-8 overflow-y-auto px-5 py-4">
        <NameForm />
        <div className="grid w-full grid-cols-2 flex-col gap-4">
          <AddItemButton id="standing-section">
            <AccountGroupIcon className="h-8 w-8 text-dark100" />
          </AddItemButton>
          <AddItemButton id="seated-section">
            <VectorRectangleIcon className="h-8 w-8 text-dark100" />
          </AddItemButton>
          <AddItemButton id="round-table">
            <DotsCircleIcon className="h-8 w-8 text-dark100" />
          </AddItemButton>
          <AddItemButton id="rectangle-table">
            <DotsSquareIcon className="h-8 w-8 text-dark100" />
          </AddItemButton>
          <AddItemButton id="landmark">
            <SignDirectionIcon className="h-8 w-8 text-dark100" />
          </AddItemButton>
        </div>
        <PerformanceDemo />
      </div>
    </div>
  );
}
