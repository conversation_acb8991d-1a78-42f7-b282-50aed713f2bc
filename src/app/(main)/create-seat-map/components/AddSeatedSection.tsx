import Disclaimer from "@components/primitive/Disclaimer";
import { CustomNumberInput } from "@components/primitive/CustomNumberInput";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { Form } from "@components/shadcn/Form";
import Name from "@components/form/Name";
import Button from "@components/primitive/Button";
import { useSeatMapContext } from "../context";
import { SeatLayoutGenerator } from "../lib/SeatLayoutGenerator";
import { SeatNumberingSystem } from "../lib/SeatNumberingSystem";
import { SeatMapEngine } from "../lib/SeatMapEngine";

const FormSchema = z.object({
  name: z.string().min(4, { message: "Name must be at least 4 char." }),
  rows: z
    .number()
    .min(1, { message: "At least 1 row required" })
    .max(1000, { message: "Max. 1000 rows allowed" }),
  seatsPerRow: z
    .number()
    .min(1, { message: "At least 1 seat required" })
    .max(1000, { message: "Max. 1000 seats per row allowed" }),
});

export default function AddSeatedSection() {
  const { stageRef } = useSeatMapContext();
  const form = useForm({
    resolver: zodResolver(FormSchema),
    defaultValues: { name: "Main Section", rows: 10, seatsPerRow: 15 },
  });

  const formErrors = form.formState.errors;
  const nameError = formErrors.name?.message;

  const onSubmit = async () => {
    try {
      // Get the seat map engine from the stage ref
      const engine = (stageRef as any)?.seatMapEngine as SeatMapEngine;
      if (!engine) {
        console.error('Seat map engine not available. Please wait for the canvas to load.');
        alert('Please wait for the canvas to load before adding sections.');
        return;
      }

      const name = form.getValues("name");
      const rows = form.getValues("rows");
      const seatsPerRow = form.getValues("seatsPerRow");

      console.log(`Creating section: ${name} with ${rows} rows and ${seatsPerRow} seats per row`);

      // Get viewport center for positioning
      const stats = engine.getStats();
      const viewport = stats.viewport;
      const centerX = viewport.width / 2;
      const centerY = viewport.height / 2;

      console.log('Viewport info:', { width: viewport.width, height: viewport.height, centerX, centerY });

      // Generate section using the new layout generator
      let section = SeatLayoutGenerator.generateRectangularSection(
        `section-${Date.now()}`,
        name,
        centerX - 100, // x position - center the section
        centerY - 100, // y position - center the section
        rows,
        seatsPerRow
      );

      // Apply numbering scheme
      const numberingConfig = SeatNumberingSystem.getPresetConfigs().standard;
      section = SeatNumberingSystem.applySectionNumbering(section, numberingConfig);

      console.log(`Generated section with ${section.seats.length} seats`);

      // Add section to the engine
      engine.addSection(section);

      // Zoom to fit the new content
      setTimeout(() => {
        engine.zoomToFit(50);
      }, 100);

      console.log(`Successfully added section "${name}" with ${section.seats.length} seats`);

      // Reset form
      form.reset({
        name: "Section " + (Date.now() % 1000),
        rows: 10,
        seatsPerRow: 15
      });

    } catch (error) {
      console.error("Error adding seated section:", error);
      alert('Error adding section. Please check the console for details.');
    }
  };

  return (
    <Form {...form}>
      <form className="flex w-full flex-col justify-center gap-4">
        <Name
          form={form}
          handleChange={(value) => form.setValue("name", value)}
          placeholder="Section name"
          showIcon={false}
        />
        <CustomNumberInput
          label="Rows"
          key="numberOfRows"
          value={form.watch("rows")}
          min={1}
          max={1000}
          onChange={(value) => form.setValue("rows", value)}
        />
        <CustomNumberInput
          label="Seats per row"
          key="seatsPerRow"
          value={form.watch("seatsPerRow")}
          min={1}
          max={1000}
          onChange={(value) => form.setValue("seatsPerRow", value)}
        />
        <Disclaimer message={nameError} variant="destructive" />
        <div className="flex justify-between">
          <Button fullWidth text="Add" onClick={form.handleSubmit(onSubmit)} />
        </div>
      </form>
    </Form>
  );
}
