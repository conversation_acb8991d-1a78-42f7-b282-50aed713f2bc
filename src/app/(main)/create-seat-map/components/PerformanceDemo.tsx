"use client";
import { useState, useEffect } from "react";
import Button from "@components/primitive/Button";
import { useSeatMapContext } from "../context";
import { SeatMapEngine } from "../lib/SeatMapEngine";
import { SeatLayoutGenerator } from "../lib/SeatLayoutGenerator";
import { SeatNumberingSystem } from "../lib/SeatNumberingSystem";

export default function PerformanceDemo() {
  const { stageRef } = useSeatMapContext();
  const [isGenerating, setIsGenerating] = useState(false);
  const [stats, setStats] = useState<any>(null);

  // Update stats periodically
  useEffect(() => {
    const interval = setInterval(() => {
      const engine = (stageRef as any)?.seatMapEngine as SeatMapEngine;
      if (engine) {
        setStats(engine.getStats());
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [stageRef]);

  const generateLargeVenue = async (rows: number, seatsPerRow: number) => {
    setIsGenerating(true);
    try {
      const engine = (stageRef as any)?.seatMapEngine as SeatMapEngine;
      if (!engine) {
        console.error('Seat map engine not available');
        return;
      }

      // Clear existing content
      engine.clear();

      console.log(`Generating venue with ${rows} rows and ${seatsPerRow} seats per row (${rows * seatsPerRow} total seats)`);

      // Generate a large rectangular section
      let section = SeatLayoutGenerator.generateRectangularSection(
        'main-section',
        `Main Section (${rows}x${seatsPerRow})`,
        50,
        50,
        rows,
        seatsPerRow
      );

      // Apply numbering
      const numberingConfig = SeatNumberingSystem.getPresetConfigs().stadium;
      section = SeatNumberingSystem.applySectionNumbering(section, numberingConfig);

      // Add to engine
      engine.addSection(section);

      // Zoom to fit
      engine.zoomToFit();

      console.log(`Successfully generated ${section.seats.length} seats`);
    } catch (error) {
      console.error('Error generating large venue:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const generateMultipleSections = async () => {
    setIsGenerating(true);
    try {
      const engine = (stageRef as any)?.seatMapEngine as SeatMapEngine;
      if (!engine) {
        console.error('Seat map engine not available');
        return;
      }

      // Clear existing content
      engine.clear();

      console.log('Generating multiple sections...');

      // Generate multiple sections of different types
      const sections = [
        // Main seating area
        SeatLayoutGenerator.generateRectangularSection(
          'main-1',
          'Main Section A',
          100,
          100,
          50,
          100
        ),
        SeatLayoutGenerator.generateRectangularSection(
          'main-2',
          'Main Section B',
          100,
          2000,
          50,
          100
        ),
        // VIP sections
        SeatLayoutGenerator.generateRectangularSection(
          'vip-1',
          'VIP Section 1',
          3000,
          100,
          20,
          30
        ),
        SeatLayoutGenerator.generateRectangularSection(
          'vip-2',
          'VIP Section 2',
          3000,
          1000,
          20,
          30
        ),
        // Balcony
        SeatLayoutGenerator.generateArcSection(
          'balcony',
          'Balcony',
          2000,
          1500,
          800,
          1200,
          Math.PI * 0.2,
          Math.PI * 0.8,
          25
        ),
        // Standing areas
        SeatLayoutGenerator.generateStandingSection(
          'standing-1',
          'Standing Area 1',
          500,
          3000,
          800,
          400,
          2000
        ),
        SeatLayoutGenerator.generateStandingSection(
          'standing-2',
          'Standing Area 2',
          2500,
          3000,
          800,
          400,
          2000
        ),
        // Round tables
        SeatLayoutGenerator.generateCircularSection(
          'table-1',
          'Table 1',
          4500,
          500,
          100,
          8
        ),
        SeatLayoutGenerator.generateCircularSection(
          'table-2',
          'Table 2',
          4500,
          800,
          100,
          8
        ),
        // Rectangle tables
        SeatLayoutGenerator.generateRectangleTableSection(
          'rect-table-1',
          'Rectangle Table 1',
          4500,
          1200,
          200,
          100,
          3
        )
      ];

      // Apply numbering and add sections
      const numberingConfigs = [
        SeatNumberingSystem.getPresetConfigs().stadium,
        SeatNumberingSystem.getPresetConfigs().theater,
        SeatNumberingSystem.getPresetConfigs().standard,
        SeatNumberingSystem.getPresetConfigs().concert
      ];

      for (let i = 0; i < sections.length; i++) {
        const section = sections[i];
        if (section.seats.length > 0) {
          const configIndex = i % numberingConfigs.length;
          const numberedSection = SeatNumberingSystem.applySectionNumbering(
            section, 
            numberingConfigs[configIndex]
          );
          engine.addSection(numberedSection);
        } else {
          engine.addSection(section);
        }
      }

      // Zoom to fit all content
      engine.zoomToFit();

      const totalSeats = sections.reduce((sum, section) => sum + section.seats.length, 0);
      console.log(`Successfully generated ${sections.length} sections with ${totalSeats} total seats`);
    } catch (error) {
      console.error('Error generating multiple sections:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const clearVenue = () => {
    const engine = (stageRef as any)?.seatMapEngine as SeatMapEngine;
    if (engine) {
      engine.clear();
      setStats(null);
    }
  };

  return (
    <div className="p-4 space-y-4 bg-white border-t border-gray-200">
      <h3 className="text-lg font-semibold">Performance Demo</h3>
      
      <div className="grid grid-cols-2 gap-2">
        <Button
          text="1K Seats (20x50)"
          onClick={() => generateLargeVenue(20, 50)}
          disabled={isGenerating}
          size="sm"
        />
        <Button
          text="10K Seats (100x100)"
          onClick={() => generateLargeVenue(100, 100)}
          disabled={isGenerating}
          size="sm"
        />
        <Button
          text="50K Seats (200x250)"
          onClick={() => generateLargeVenue(200, 250)}
          disabled={isGenerating}
          size="sm"
        />
        <Button
          text="100K Seats (500x200)"
          onClick={() => generateLargeVenue(500, 200)}
          disabled={isGenerating}
          size="sm"
        />
      </div>

      <div className="grid grid-cols-2 gap-2">
        <Button
          text="Multiple Sections"
          onClick={generateMultipleSections}
          disabled={isGenerating}
          size="sm"
        />
        <Button
          text="Clear All"
          onClick={clearVenue}
          disabled={isGenerating}
          size="sm"
          variant="secondary"
        />
      </div>

      {isGenerating && (
        <div className="text-sm text-gray-600">
          Generating seats... This may take a moment for large venues.
        </div>
      )}

      {stats && (
        <div className="text-xs space-y-1 bg-gray-50 p-2 rounded">
          <div><strong>Performance Stats:</strong></div>
          <div>Total Seats: {stats.data.totalSeats.toLocaleString()}</div>
          <div>Visible Seats: {stats.performance.visibleSeatCount.toLocaleString()}</div>
          <div>Sections: {stats.data.totalSections}</div>
          <div>LOD Level: {stats.virtualization.description}</div>
          <div>Render Time: {stats.performance.renderTime.toFixed(2)}ms</div>
          <div>Frame Rate: {stats.performance.frameRate.toFixed(1)} FPS</div>
          <div>Memory: {stats.performance.memoryUsage.toFixed(1)} MB</div>
          <div>Scale: {stats.viewport.scale.toFixed(2)}x</div>
        </div>
      )}
    </div>
  );
}
