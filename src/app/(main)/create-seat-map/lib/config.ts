export const LAYOUT_HEIGHT = 500;
export const LAYOUT_WIDTH = 500;
export const SEAT_SIZE = 20;
export const SEAT_GAP = 8;
export const SHAPE_PADDING = 10;

// Performance optimization constants
export const VIEWPORT_PADDING = 100; // Extra padding around viewport for smooth scrolling
export const LOD_THRESHOLD_1 = 0.5; // Below this zoom, show simplified seats
export const LOD_THRESHOLD_2 = 0.2; // Below this zoom, show seat density only
export const MAX_SEATS_PER_FRAME = 1000; // Maximum seats to render per frame
export const SPATIAL_INDEX_CELL_SIZE = 100; // Grid cell size for spatial indexing
export const CACHE_INVALIDATION_THRESHOLD = 50; // Number of changes before cache invalidation
