import { Viewport, SeatMapEvent } from './types';
import { LOD_THRESHOLD_1, LOD_THRESHOLD_2 } from './config';

/**
 * Manages viewport state, zoom, pan operations, and level-of-detail calculations
 */
export class ViewportManager {
  private viewport: Viewport;
  private eventListeners: ((event: SeatMapEvent) => void)[] = [];
  private minScale: number = 0.1;
  private maxScale: number = 10;
  private isDirty: boolean = false;

  constructor(initialViewport: Viewport) {
    this.viewport = { ...initialViewport };
  }

  /**
   * Get current viewport
   */
  getViewport(): Viewport {
    return { ...this.viewport };
  }

  /**
   * Set viewport position
   */
  setPosition(x: number, y: number): void {
    if (this.viewport.x !== x || this.viewport.y !== y) {
      this.viewport.x = x;
      this.viewport.y = y;
      this.markDirty();
      this.emitViewportChanged();
    }
  }

  /**
   * Set viewport scale with bounds checking
   */
  setScale(scale: number): void {
    const clampedScale = Math.max(this.minScale, Math.min(this.maxScale, scale));
    if (this.viewport.scale !== clampedScale) {
      this.viewport.scale = clampedScale;
      this.markDirty();
      this.emitViewportChanged();
    }
  }

  /**
   * Set viewport dimensions
   */
  setDimensions(width: number, height: number): void {
    if (this.viewport.width !== width || this.viewport.height !== height) {
      this.viewport.width = width;
      this.viewport.height = height;
      this.markDirty();
      this.emitViewportChanged();
    }
  }

  /**
   * Pan the viewport by delta amounts
   */
  pan(deltaX: number, deltaY: number): void {
    this.setPosition(this.viewport.x + deltaX, this.viewport.y + deltaY);
  }

  /**
   * Zoom at a specific point (like mouse position)
   */
  zoomAtPoint(scale: number, pointX: number, pointY: number): void {
    const oldScale = this.viewport.scale;
    const newScale = Math.max(this.minScale, Math.min(this.maxScale, scale));
    
    if (oldScale === newScale) return;

    // Calculate the world coordinates of the zoom point
    const worldX = (pointX - this.viewport.x) / oldScale;
    const worldY = (pointY - this.viewport.y) / oldScale;

    // Update scale
    this.viewport.scale = newScale;

    // Adjust position to keep the zoom point stationary
    this.viewport.x = pointX - worldX * newScale;
    this.viewport.y = pointY - worldY * newScale;

    this.markDirty();
    this.emitViewportChanged();
  }

  /**
   * Zoom to fit all content within the viewport
   */
  zoomToFit(contentBounds: { minX: number; minY: number; maxX: number; maxY: number }, padding: number = 50): void {
    const contentWidth = contentBounds.maxX - contentBounds.minX;
    const contentHeight = contentBounds.maxY - contentBounds.minY;
    
    if (contentWidth === 0 || contentHeight === 0) return;

    const scaleX = (this.viewport.width - padding * 2) / contentWidth;
    const scaleY = (this.viewport.height - padding * 2) / contentHeight;
    const scale = Math.min(scaleX, scaleY);

    // Center the content
    const centerX = (contentBounds.minX + contentBounds.maxX) / 2;
    const centerY = (contentBounds.minY + contentBounds.maxY) / 2;
    
    this.viewport.scale = Math.max(this.minScale, Math.min(this.maxScale, scale));
    this.viewport.x = this.viewport.width / 2 - centerX * this.viewport.scale;
    this.viewport.y = this.viewport.height / 2 - centerY * this.viewport.scale;

    this.markDirty();
    this.emitViewportChanged();
  }

  /**
   * Center viewport on a specific point
   */
  centerOn(worldX: number, worldY: number): void {
    this.viewport.x = this.viewport.width / 2 - worldX * this.viewport.scale;
    this.viewport.y = this.viewport.height / 2 - worldY * this.viewport.scale;
    this.markDirty();
    this.emitViewportChanged();
  }

  /**
   * Convert screen coordinates to world coordinates
   */
  screenToWorld(screenX: number, screenY: number): { x: number; y: number } {
    return {
      x: (screenX - this.viewport.x) / this.viewport.scale,
      y: (screenY - this.viewport.y) / this.viewport.scale
    };
  }

  /**
   * Convert world coordinates to screen coordinates
   */
  worldToScreen(worldX: number, worldY: number): { x: number; y: number } {
    return {
      x: worldX * this.viewport.scale + this.viewport.x,
      y: worldY * this.viewport.scale + this.viewport.y
    };
  }

  /**
   * Get the world bounds of the current viewport
   */
  getWorldBounds(): { minX: number; minY: number; maxX: number; maxY: number } {
    const topLeft = this.screenToWorld(0, 0);
    const bottomRight = this.screenToWorld(this.viewport.width, this.viewport.height);
    
    return {
      minX: topLeft.x,
      minY: topLeft.y,
      maxX: bottomRight.x,
      maxY: bottomRight.y
    };
  }

  /**
   * Calculate level of detail based on current scale
   */
  getLODLevel(): number {
    if (this.viewport.scale < LOD_THRESHOLD_2) return 2; // Density only
    if (this.viewport.scale < LOD_THRESHOLD_1) return 1; // Simplified
    return 0; // Full detail
  }

  /**
   * Check if a world-space rectangle is visible in the viewport
   */
  isRectangleVisible(x: number, y: number, width: number, height: number, padding: number = 0): boolean {
    const bounds = this.getWorldBounds();
    return !(x + width < bounds.minX - padding ||
             x > bounds.maxX + padding ||
             y + height < bounds.minY - padding ||
             y > bounds.maxY + padding);
  }

  /**
   * Check if a point is visible in the viewport
   */
  isPointVisible(x: number, y: number, padding: number = 0): boolean {
    const bounds = this.getWorldBounds();
    return x >= bounds.minX - padding &&
           x <= bounds.maxX + padding &&
           y >= bounds.minY - padding &&
           y <= bounds.maxY + padding;
  }

  /**
   * Set scale bounds
   */
  setScaleBounds(minScale: number, maxScale: number): void {
    this.minScale = minScale;
    this.maxScale = maxScale;
    
    // Clamp current scale if needed
    const clampedScale = Math.max(minScale, Math.min(maxScale, this.viewport.scale));
    if (clampedScale !== this.viewport.scale) {
      this.setScale(clampedScale);
    }
  }

  /**
   * Get scale bounds
   */
  getScaleBounds(): { min: number; max: number } {
    return { min: this.minScale, max: this.maxScale };
  }

  /**
   * Check if viewport has changed since last check
   */
  isDirtyViewport(): boolean {
    return this.isDirty;
  }

  /**
   * Mark viewport as clean after processing
   */
  markClean(): void {
    this.isDirty = false;
  }

  /**
   * Add event listener
   */
  addEventListener(listener: (event: SeatMapEvent) => void): void {
    this.eventListeners.push(listener);
  }

  /**
   * Remove event listener
   */
  removeEventListener(listener: (event: SeatMapEvent) => void): void {
    const index = this.eventListeners.indexOf(listener);
    if (index !== -1) {
      this.eventListeners.splice(index, 1);
    }
  }

  // Private methods

  private markDirty(): void {
    this.isDirty = true;
  }

  private emitViewportChanged(): void {
    const event: SeatMapEvent = {
      type: 'viewport-changed',
      viewport: this.getViewport()
    };

    this.eventListeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        console.error('Error in viewport event listener:', error);
      }
    });
  }
}
