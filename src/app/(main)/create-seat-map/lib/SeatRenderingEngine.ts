import Konva from 'konva';
import { Seat, SeatSection, RenderContext, SectionStyle } from './types';
import { SEAT_SIZE, SEAT_GAP } from './config';

/**
 * High-performance seat rendering engine using optimized Konva techniques
 * Uses canvas caching, batch rendering, and efficient drawing operations
 */
export class SeatRenderingEngine {
  private layer: Konva.Layer;
  private seatCache: Map<string, Konva.Group> = new Map();
  private sectionCache: Map<string, Konva.Group> = new Map();
  private batchRenderer: Konva.Shape | null = null;
  private isDirty: boolean = true;

  constructor(layer: Konva.Layer) {
    this.layer = layer;
    this.setupBatchRenderer();
  }

  /**
   * Render the complete seat map based on render context
   */
  render(renderContext: RenderContext): void {
    const startTime = performance.now();

    console.log('SeatRenderingEngine: Starting render with', {
      visibleSeats: renderContext.visibleSeats.length,
      visibleSections: renderContext.visibleSections.length,
      lodLevel: renderContext.lodLevel,
      isDirty: this.isDirty || renderContext.isDirty
    });

    // Clear previous render if needed
    if (this.isDirty || renderContext.isDirty) {
      this.clearRender();
    }

    // Render based on LOD level
    switch (renderContext.lodLevel) {
      case 0:
        this.renderFullDetail(renderContext);
        break;
      case 1:
        this.renderSimplified(renderContext);
        break;
      case 2:
        this.renderDensityOnly(renderContext);
        break;
    }

    this.layer.batchDraw();
    this.isDirty = false;

    const endTime = performance.now();
    console.log(`SeatRenderingEngine: Render completed in ${endTime - startTime}ms, Seats: ${renderContext.visibleSeats.length}`);
  }

  /**
   * Render full detail - individual seat objects with labels
   */
  private renderFullDetail(renderContext: RenderContext): void {
    console.log('SeatRenderingEngine: Rendering full detail with', renderContext.visibleSections.length, 'sections and', renderContext.visibleSeats.length, 'seats');

    // Render sections first
    renderContext.visibleSections.forEach((section, index) => {
      console.log(`SeatRenderingEngine: Rendering section ${index + 1}:`, section.name, 'at', section.x, section.y);
      this.renderSection(section, renderContext);
    });

    // Render individual seats
    renderContext.visibleSeats.forEach((seat, index) => {
      if (index < 5) { // Only log first 5 seats to avoid spam
        console.log(`SeatRenderingEngine: Rendering seat ${index + 1}:`, seat.id, 'at', seat.x, seat.y);
      }
      this.renderSeat(seat, renderContext);
    });
  }

  /**
   * Render simplified - batch rendering without labels
   */
  private renderSimplified(renderContext: RenderContext): void {
    // Render sections
    renderContext.visibleSections.forEach(section => {
      this.renderSectionSimplified(section, renderContext);
    });

    // Batch render seats
    this.renderSeatsBatch(renderContext.visibleSeats, renderContext);
  }

  /**
   * Render density only - show section outlines and density indicators
   */
  private renderDensityOnly(renderContext: RenderContext): void {
    renderContext.visibleSections.forEach(section => {
      this.renderSectionDensity(section, renderContext);
    });

    // Render only selected/hovered seats individually
    const importantSeats = renderContext.visibleSeats.filter(
      seat => seat.isSelected || seat.isHovered
    );
    importantSeats.forEach(seat => {
      this.renderSeat(seat, renderContext);
    });
  }

  /**
   * Render individual seat with full detail
   */
  private renderSeat(seat: Seat, renderContext: RenderContext): void {
    const cacheKey = `seat-${seat.id}`;
    let seatGroup = this.seatCache.get(cacheKey);

    if (!seatGroup) {
      seatGroup = new Konva.Group({
        id: seat.id,
        x: seat.x,
        y: seat.y,
        listening: true
      });

      // Seat rectangle
      const seatRect = new Konva.Rect({
        width: SEAT_SIZE,
        height: SEAT_SIZE,
        cornerRadius: 2,
        perfectDrawEnabled: false
      });

      // Seat label
      const seatLabel = new Konva.Text({
        width: SEAT_SIZE,
        height: SEAT_SIZE,
        text: seat.label || `${seat.row}-${seat.seatNumber}`,
        fontSize: Math.min(SEAT_SIZE / 3.5, 10),
        fontFamily: 'Arial',
        align: 'center',
        verticalAlign: 'middle',
        perfectDrawEnabled: false,
        listening: false
      });

      seatGroup.add(seatRect);
      seatGroup.add(seatLabel);
      this.layer.add(seatGroup);
      this.seatCache.set(cacheKey, seatGroup);
    }

    // Update seat appearance based on state
    this.updateSeatAppearance(seatGroup, seat);
    seatGroup.visible(true);
  }

  /**
   * Update seat appearance based on current state
   */
  private updateSeatAppearance(seatGroup: Konva.Group, seat: Seat): void {
    const seatRect = seatGroup.findOne('Rect') as Konva.Rect;
    const seatLabel = seatGroup.findOne('Text') as Konva.Text;

    if (!seatRect || !seatLabel) return;

    // Determine colors based on seat state
    let fillColor = '#4F46E5'; // Default blue
    let strokeColor = '#312E81';
    let textColor = 'white';

    if (!seat.isActive) {
      fillColor = '#9CA3AF'; // Gray for inactive
      strokeColor = '#6B7280';
      textColor = '#374151';
    } else if (seat.isSelected) {
      fillColor = '#10B981'; // Green for selected
      strokeColor = '#047857';
    } else if (seat.isHovered) {
      fillColor = '#F59E0B'; // Orange for hovered
      strokeColor = '#D97706';
    }

    seatRect.fill(fillColor);
    seatRect.stroke(strokeColor);
    seatRect.strokeWidth(1);
    seatLabel.fill(textColor);

    // Update position if needed
    seatGroup.x(seat.x);
    seatGroup.y(seat.y);
  }

  /**
   * Batch render seats for performance
   */
  private renderSeatsBatch(seats: Seat[], renderContext: RenderContext): void {
    if (!this.batchRenderer) return;

    this.batchRenderer.sceneFunc((context, shape) => {
      seats.forEach(seat => {
        this.drawSeatDirect(context, seat);
      });
    });

    this.batchRenderer.visible(true);
  }

  /**
   * Draw seat directly on canvas context for maximum performance
   */
  private drawSeatDirect(context: CanvasRenderingContext2D, seat: Seat): void {
    // Determine colors based on seat state
    let fillColor = '#4F46E5';
    if (!seat.isActive) fillColor = '#9CA3AF';
    else if (seat.isSelected) fillColor = '#10B981';
    else if (seat.isHovered) fillColor = '#F59E0B';

    context.fillStyle = fillColor;
    context.fillRect(seat.x, seat.y, SEAT_SIZE, SEAT_SIZE);

    // Add stroke
    context.strokeStyle = '#312E81';
    context.lineWidth = 1;
    context.strokeRect(seat.x, seat.y, SEAT_SIZE, SEAT_SIZE);
  }

  /**
   * Render section with full detail
   */
  private renderSection(section: SeatSection, renderContext: RenderContext): void {
    const cacheKey = `section-${section.id}`;
    let sectionGroup = this.sectionCache.get(cacheKey);

    if (!sectionGroup) {
      sectionGroup = new Konva.Group({
        id: section.id,
        x: section.x,
        y: section.y,
        listening: false
      });

      // Section background
      const sectionRect = new Konva.Rect({
        width: section.width,
        height: section.height,
        fill: section.style.fillColor,
        stroke: section.style.strokeColor,
        strokeWidth: section.style.strokeWidth,
        opacity: 0.3,
        perfectDrawEnabled: false,
        listening: false
      });

      // Section label
      const sectionLabel = new Konva.Text({
        x: 10,
        y: 10,
        text: section.name,
        fontSize: 14,
        fontFamily: 'Arial',
        fill: section.style.textColor,
        perfectDrawEnabled: false,
        listening: false
      });

      sectionGroup.add(sectionRect);
      sectionGroup.add(sectionLabel);
      this.layer.add(sectionGroup);
      this.sectionCache.set(cacheKey, sectionGroup);
    }

    sectionGroup.visible(true);
  }

  /**
   * Render section in simplified mode
   */
  private renderSectionSimplified(section: SeatSection, renderContext: RenderContext): void {
    // Just render the section outline
    this.renderSection(section, renderContext);
  }

  /**
   * Render section density indicator
   */
  private renderSectionDensity(section: SeatSection, renderContext: RenderContext): void {
    const cacheKey = `section-density-${section.id}`;
    let densityGroup = this.sectionCache.get(cacheKey);

    if (!densityGroup) {
      densityGroup = new Konva.Group({
        id: `${section.id}-density`,
        x: section.x,
        y: section.y,
        listening: false
      });

      // Section outline
      const outline = new Konva.Rect({
        width: section.width,
        height: section.height,
        stroke: section.style.strokeColor,
        strokeWidth: 2,
        perfectDrawEnabled: false,
        listening: false
      });

      // Density indicator
      const density = section.seats.length / (section.width * section.height) * 10000;
      const densityText = new Konva.Text({
        x: section.width / 2,
        y: section.height / 2,
        text: `${Math.round(density)} seats`,
        fontSize: 12,
        fontFamily: 'Arial',
        fill: section.style.textColor,
        align: 'center',
        offsetX: 30,
        offsetY: 6,
        perfectDrawEnabled: false,
        listening: false
      });

      densityGroup.add(outline);
      densityGroup.add(densityText);
      this.layer.add(densityGroup);
      this.sectionCache.set(cacheKey, densityGroup);
    }

    densityGroup.visible(true);
  }

  /**
   * Setup batch renderer for high-performance drawing
   */
  private setupBatchRenderer(): void {
    this.batchRenderer = new Konva.Shape({
      sceneFunc: () => {}, // Will be set dynamically
      listening: false,
      perfectDrawEnabled: false
    });
    this.layer.add(this.batchRenderer);
  }

  /**
   * Clear all rendered content
   */
  private clearRender(): void {
    // Hide cached objects instead of destroying them
    this.seatCache.forEach(seatGroup => {
      seatGroup.visible(false);
    });

    this.sectionCache.forEach(sectionGroup => {
      sectionGroup.visible(false);
    });

    if (this.batchRenderer) {
      this.batchRenderer.visible(false);
    }
  }

  /**
   * Update specific seat without full re-render
   */
  updateSeat(seat: Seat): void {
    const cacheKey = `seat-${seat.id}`;
    const seatGroup = this.seatCache.get(cacheKey);
    
    if (seatGroup) {
      this.updateSeatAppearance(seatGroup, seat);
      this.layer.batchDraw();
    }
  }

  /**
   * Force cache invalidation
   */
  invalidateCache(): void {
    this.isDirty = true;
  }

  /**
   * Clean up resources
   */
  destroy(): void {
    this.seatCache.clear();
    this.sectionCache.clear();
    if (this.batchRenderer) {
      this.batchRenderer.destroy();
    }
  }

  /**
   * Get rendering statistics
   */
  getStats() {
    return {
      cachedSeats: this.seatCache.size,
      cachedSections: this.sectionCache.size,
      isDirty: this.isDirty
    };
  }
}
