import { PerformanceMetrics, SeatMapEvent } from './types';

/**
 * Performance monitoring and optimization system
 * Tracks rendering performance, memory usage, and provides optimization suggestions
 */
export class PerformanceMonitor {
  private metrics: PerformanceMetrics[] = [];
  private maxHistorySize: number = 100;
  private eventListeners: ((event: SeatMapEvent) => void)[] = [];
  private monitoringInterval: NodeJS.Timeout | null = null;
  private isMonitoring: boolean = false;
  private lastWarningTime: number = 0;
  private warningCooldown: number = 5000; // 5 seconds between warnings

  constructor() {
    this.startMonitoring();
  }

  /**
   * Record a performance metric
   */
  recordMetric(metric: PerformanceMetrics): void {
    this.metrics.push({
      ...metric,
      timestamp: Date.now()
    } as PerformanceMetrics & { timestamp: number });

    // Keep only recent metrics
    if (this.metrics.length > this.maxHistorySize) {
      this.metrics.shift();
    }

    // Emit performance update event
    this.emitEvent({
      type: 'performance-update',
      metrics: metric
    });

    // Check for performance issues
    this.checkPerformanceIssues(metric);
  }

  /**
   * Get current performance statistics
   */
  getCurrentStats(): {
    current: PerformanceMetrics;
    average: PerformanceMetrics;
    peak: PerformanceMetrics;
    recommendations: string[];
  } {
    if (this.metrics.length === 0) {
      const emptyMetric: PerformanceMetrics = {
        renderTime: 0,
        seatCount: 0,
        visibleSeatCount: 0,
        frameRate: 0,
        memoryUsage: 0
      };
      return {
        current: emptyMetric,
        average: emptyMetric,
        peak: emptyMetric,
        recommendations: []
      };
    }

    const current = this.metrics[this.metrics.length - 1];
    const average = this.calculateAverage();
    const peak = this.calculatePeak();
    const recommendations = this.generateRecommendations();

    return { current, average, peak, recommendations };
  }

  /**
   * Get performance trend over time
   */
  getPerformanceTrend(): {
    renderTimes: number[];
    frameRates: number[];
    memoryUsage: number[];
    timestamps: number[];
  } {
    return {
      renderTimes: this.metrics.map(m => m.renderTime),
      frameRates: this.metrics.map(m => m.frameRate),
      memoryUsage: this.metrics.map(m => m.memoryUsage),
      timestamps: this.metrics.map(m => (m as any).timestamp || Date.now())
    };
  }

  /**
   * Check for performance issues and emit warnings
   */
  private checkPerformanceIssues(metric: PerformanceMetrics): void {
    const now = Date.now();

    // Only check for issues if we have content and enough time has passed
    if (metric.seatCount === 0 || now - this.lastWarningTime < this.warningCooldown) {
      return;
    }

    const issues: string[] = [];

    // Check render time (only if we have content)
    if (metric.renderTime > 33 && metric.seatCount > 0) { // 30 FPS threshold, more lenient
      issues.push(`High render time: ${metric.renderTime.toFixed(2)}ms`);
    }

    // Check frame rate (only if we have content and it's significantly low)
    if (metric.frameRate < 20 && metric.seatCount > 0) {
      issues.push(`Low frame rate: ${metric.frameRate.toFixed(1)} FPS`);
    }

    // Check memory usage
    if (metric.memoryUsage > 150) { // 150MB threshold, more lenient
      issues.push(`High memory usage: ${metric.memoryUsage.toFixed(1)}MB`);
    }

    // Check visible seat count vs total
    const visibilityRatio = metric.seatCount > 0 ? metric.visibleSeatCount / metric.seatCount : 0;
    if (visibilityRatio > 0.7 && metric.seatCount > 20000) {
      issues.push(`Too many visible seats: ${metric.visibleSeatCount} of ${metric.seatCount}`);
    }

    if (issues.length > 0) {
      console.warn('Performance issues detected:', issues);
      this.lastWarningTime = now;
    }
  }

  /**
   * Generate performance recommendations
   */
  private generateRecommendations(): string[] {
    const recommendations: string[] = [];
    const recent = this.metrics.slice(-10); // Last 10 metrics

    if (recent.length === 0) return recommendations;

    const avgRenderTime = recent.reduce((sum, m) => sum + m.renderTime, 0) / recent.length;
    const avgFrameRate = recent.reduce((sum, m) => sum + m.frameRate, 0) / recent.length;
    const avgMemory = recent.reduce((sum, m) => sum + m.memoryUsage, 0) / recent.length;
    const avgVisibleSeats = recent.reduce((sum, m) => sum + m.visibleSeatCount, 0) / recent.length;

    // Render time recommendations
    if (avgRenderTime > 20) {
      recommendations.push('Consider reducing visible seat count or enabling more aggressive LOD');
    }

    // Frame rate recommendations
    if (avgFrameRate < 30) {
      recommendations.push('Enable viewport culling and increase LOD thresholds');
    }

    // Memory recommendations
    if (avgMemory > 80) {
      recommendations.push('Consider implementing progressive loading or reducing cached objects');
    }

    // Visible seats recommendations
    if (avgVisibleSeats > 5000) {
      recommendations.push('Implement more aggressive virtualization for large seat counts');
    }

    // General recommendations
    if (recommendations.length === 0) {
      recommendations.push('Performance is optimal');
    }

    return recommendations;
  }

  /**
   * Calculate average metrics
   */
  private calculateAverage(): PerformanceMetrics {
    if (this.metrics.length === 0) {
      return {
        renderTime: 0,
        seatCount: 0,
        visibleSeatCount: 0,
        frameRate: 0,
        memoryUsage: 0
      };
    }

    const sum = this.metrics.reduce((acc, metric) => ({
      renderTime: acc.renderTime + metric.renderTime,
      seatCount: acc.seatCount + metric.seatCount,
      visibleSeatCount: acc.visibleSeatCount + metric.visibleSeatCount,
      frameRate: acc.frameRate + metric.frameRate,
      memoryUsage: acc.memoryUsage + metric.memoryUsage
    }), {
      renderTime: 0,
      seatCount: 0,
      visibleSeatCount: 0,
      frameRate: 0,
      memoryUsage: 0
    });

    const count = this.metrics.length;
    return {
      renderTime: sum.renderTime / count,
      seatCount: sum.seatCount / count,
      visibleSeatCount: sum.visibleSeatCount / count,
      frameRate: sum.frameRate / count,
      memoryUsage: sum.memoryUsage / count
    };
  }

  /**
   * Calculate peak metrics
   */
  private calculatePeak(): PerformanceMetrics {
    if (this.metrics.length === 0) {
      return {
        renderTime: 0,
        seatCount: 0,
        visibleSeatCount: 0,
        frameRate: 0,
        memoryUsage: 0
      };
    }

    return this.metrics.reduce((peak, metric) => ({
      renderTime: Math.max(peak.renderTime, metric.renderTime),
      seatCount: Math.max(peak.seatCount, metric.seatCount),
      visibleSeatCount: Math.max(peak.visibleSeatCount, metric.visibleSeatCount),
      frameRate: Math.max(peak.frameRate, metric.frameRate),
      memoryUsage: Math.max(peak.memoryUsage, metric.memoryUsage)
    }));
  }

  /**
   * Start performance monitoring
   */
  private startMonitoring(): void {
    if (this.isMonitoring) return;

    this.isMonitoring = true;
    
    // Monitor browser performance API
    if (typeof window !== 'undefined' && window.performance) {
      this.monitoringInterval = setInterval(() => {
        this.collectBrowserMetrics();
      }, 1000);
    }
  }

  /**
   * Stop performance monitoring
   */
  stopMonitoring(): void {
    this.isMonitoring = false;
    
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
  }

  /**
   * Collect browser performance metrics
   */
  private collectBrowserMetrics(): void {
    if (typeof window === 'undefined' || !window.performance) return;

    try {
      // Get memory info if available
      let memoryUsage = 0;
      if ('memory' in window.performance) {
        const memory = (window.performance as any).memory;
        memoryUsage = memory.usedJSHeapSize / 1024 / 1024; // Convert to MB
      }

      // This will be updated by the actual rendering engine
      // We're just providing a baseline here
    } catch (error) {
      console.warn('Failed to collect browser metrics:', error);
    }
  }

  /**
   * Export performance data for analysis
   */
  exportData(): string {
    const data = {
      metrics: this.metrics,
      stats: this.getCurrentStats(),
      trend: this.getPerformanceTrend(),
      timestamp: new Date().toISOString()
    };

    return JSON.stringify(data, null, 2);
  }

  /**
   * Clear performance history
   */
  clearHistory(): void {
    this.metrics = [];
  }

  /**
   * Add event listener
   */
  addEventListener(listener: (event: SeatMapEvent) => void): void {
    this.eventListeners.push(listener);
  }

  /**
   * Remove event listener
   */
  removeEventListener(listener: (event: SeatMapEvent) => void): void {
    const index = this.eventListeners.indexOf(listener);
    if (index !== -1) {
      this.eventListeners.splice(index, 1);
    }
  }

  /**
   * Emit event to listeners
   */
  private emitEvent(event: SeatMapEvent): void {
    this.eventListeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        console.error('Error in performance monitor event listener:', error);
      }
    });
  }

  /**
   * Destroy the monitor
   */
  destroy(): void {
    this.stopMonitoring();
    this.eventListeners = [];
    this.metrics = [];
  }
}
