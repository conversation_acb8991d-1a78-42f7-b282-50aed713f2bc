// Core seat mapping types for high-performance rendering

export interface Seat {
  id: string;
  x: number;
  y: number;
  row: number;
  seatNumber: number;
  sectionId: string;
  isActive: boolean;
  isSelected: boolean;
  isHovered: boolean;
  label?: string;
  customData?: Record<string, any>;
}

export interface SeatSection {
  id: string;
  name: string;
  type: 'seated' | 'standing' | 'table-round' | 'table-rectangle' | 'landmark';
  x: number;
  y: number;
  width: number;
  height: number;
  rotation: number;
  seats: Seat[];
  capacity: number;
  shape: SectionShape;
  style: SectionStyle;
}

export interface SectionShape {
  type: 'rectangle' | 'arc' | 'polygon' | 'circle';
  points?: number[]; // For polygon shapes
  radius?: number; // For circle/arc shapes
  startAngle?: number; // For arc shapes
  endAngle?: number; // For arc shapes
}

export interface SectionStyle {
  fillColor: string;
  strokeColor: string;
  strokeWidth: number;
  seatColor: string;
  selectedSeatColor: string;
  inactiveSeatColor: string;
  textColor: string;
  fontSize: number;
}

export interface Viewport {
  x: number;
  y: number;
  width: number;
  height: number;
  scale: number;
}

export interface SpatialBounds {
  minX: number;
  minY: number;
  maxX: number;
  maxY: number;
}

export interface SpatialIndexCell {
  bounds: SpatialBounds;
  seats: Seat[];
  sections: SeatSection[];
}

export interface RenderContext {
  viewport: Viewport;
  lodLevel: number; // 0 = full detail, 1 = simplified, 2 = density only
  visibleSeats: Seat[];
  visibleSections: SeatSection[];
  isDirty: boolean;
}

export interface SeatMapConfig {
  maxSeatsPerFrame: number;
  viewportPadding: number;
  lodThresholds: number[];
  spatialIndexCellSize: number;
  enableCaching: boolean;
  enableVirtualization: boolean;
}

export interface SeatInteraction {
  type: 'click' | 'hover' | 'drag';
  seatId: string;
  position: { x: number; y: number };
  timestamp: number;
}

export interface PerformanceMetrics {
  renderTime: number;
  seatCount: number;
  visibleSeatCount: number;
  frameRate: number;
  memoryUsage: number;
}

// Event types for seat map interactions
export type SeatMapEvent = 
  | { type: 'seat-selected'; seat: Seat }
  | { type: 'seat-deselected'; seat: Seat }
  | { type: 'seat-hovered'; seat: Seat }
  | { type: 'seat-unhovered'; seat: Seat }
  | { type: 'section-selected'; section: SeatSection }
  | { type: 'viewport-changed'; viewport: Viewport }
  | { type: 'performance-update'; metrics: PerformanceMetrics };

// Utility types
export type SeatState = 'active' | 'inactive' | 'selected' | 'hovered';
export type RenderMode = 'full' | 'simplified' | 'density';
