import Konva from 'konva';
import { Seat, SeatSection, SeatInteraction, SeatMapEvent } from './types';
import { SeatDataManager } from './SeatDataManager';
import { ViewportManager } from './ViewportManager';
import { SeatRenderingEngine } from './SeatRenderingEngine';
import { SEAT_SIZE } from './config';

/**
 * Manages user interactions with the seat map including clicks, hovers, and selections
 * Optimized for high-performance interaction with large numbers of seats
 */
export class InteractionManager {
  private stage: Konva.Stage;
  private dataManager: SeatDataManager;
  private viewportManager: ViewportManager;
  private renderingEngine: SeatRenderingEngine;
  private eventListeners: ((event: SeatMapEvent) => void)[] = [];
  
  private isMouseDown: boolean = false;
  private isDragging: boolean = false;
  private lastPointerPosition: { x: number; y: number } | null = null;
  private dragThreshold: number = 5;
  private hoverTimeout: NodeJS.Timeout | null = null;
  private hoverDelay: number = 100; // ms

  constructor(
    stage: Konva.Stage,
    dataManager: SeatDataManager,
    viewportManager: ViewportManager,
    renderingEngine: SeatRenderingEngine
  ) {
    this.stage = stage;
    this.dataManager = dataManager;
    this.viewportManager = viewportManager;
    this.renderingEngine = renderingEngine;
    
    this.setupEventListeners();
  }

  /**
   * Setup all event listeners for the stage
   */
  private setupEventListeners(): void {
    // Mouse/touch events
    this.stage.on('mousedown touchstart', this.handlePointerDown.bind(this));
    this.stage.on('mousemove touchmove', this.handlePointerMove.bind(this));
    this.stage.on('mouseup touchend', this.handlePointerUp.bind(this));
    this.stage.on('click tap', this.handleClick.bind(this));
    
    // Wheel events for zooming
    this.stage.on('wheel', this.handleWheel.bind(this));
    
    // Context menu
    this.stage.on('contextmenu', this.handleContextMenu.bind(this));
    
    // Mouse leave to clear hover states
    this.stage.on('mouseleave', this.handleMouseLeave.bind(this));
  }

  /**
   * Handle pointer down events
   */
  private handlePointerDown(e: Konva.KonvaEventObject<MouseEvent | TouchEvent>): void {
    e.evt.preventDefault();
    
    this.isMouseDown = true;
    this.isDragging = false;
    
    const pos = this.stage.getPointerPosition();
    if (pos) {
      this.lastPointerPosition = { x: pos.x, y: pos.y };
    }
  }

  /**
   * Handle pointer move events
   */
  private handlePointerMove(e: Konva.KonvaEventObject<MouseEvent | TouchEvent>): void {
    const pos = this.stage.getPointerPosition();
    if (!pos) return;

    // Handle dragging
    if (this.isMouseDown && this.lastPointerPosition) {
      const deltaX = pos.x - this.lastPointerPosition.x;
      const deltaY = pos.y - this.lastPointerPosition.y;
      const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

      if (distance > this.dragThreshold) {
        this.isDragging = true;
        this.handlePan(deltaX, deltaY);
        this.lastPointerPosition = { x: pos.x, y: pos.y };
      }
    }

    // Handle hover with debouncing
    if (!this.isDragging) {
      this.handleHoverDebounced(pos.x, pos.y);
    }
  }

  /**
   * Handle pointer up events
   */
  private handlePointerUp(e: Konva.KonvaEventObject<MouseEvent | TouchEvent>): void {
    this.isMouseDown = false;
    
    // Reset dragging state after a short delay to prevent click events
    setTimeout(() => {
      this.isDragging = false;
    }, 10);
  }

  /**
   * Handle click events
   */
  private handleClick(e: Konva.KonvaEventObject<MouseEvent | TouchEvent>): void {
    // Ignore clicks if we were dragging
    if (this.isDragging) return;

    const pos = this.stage.getPointerPosition();
    if (!pos) return;

    // Convert screen coordinates to world coordinates
    const worldPos = this.viewportManager.screenToWorld(pos.x, pos.y);
    
    // Find seat at click position
    const seat = this.dataManager.getSeatAtPoint(worldPos.x, worldPos.y, SEAT_SIZE / 2);
    
    if (seat) {
      this.handleSeatClick(seat, e.evt);
    } else {
      // Click on empty space - clear selection if not holding Ctrl/Cmd
      if (!e.evt.ctrlKey && !e.evt.metaKey) {
        this.dataManager.clearSelection();
      }
    }
  }

  /**
   * Handle seat click
   */
  private handleSeatClick(seat: Seat, originalEvent: Event): void {
    if (!seat.isActive) return;

    const isMultiSelect = (originalEvent as MouseEvent).ctrlKey || 
                         (originalEvent as MouseEvent).metaKey;

    if (isMultiSelect) {
      // Multi-select mode
      this.dataManager.toggleSeatSelection(seat.id);
    } else {
      // Single select mode
      if (!seat.isSelected) {
        this.dataManager.clearSelection();
        this.dataManager.toggleSeatSelection(seat.id);
      }
    }

    // Update rendering for the affected seat
    this.renderingEngine.updateSeat(seat);

    // Emit interaction event
    this.emitEvent({
      type: seat.isSelected ? 'seat-selected' : 'seat-deselected',
      seat
    });
  }

  /**
   * Handle hover with debouncing
   */
  private handleHoverDebounced(screenX: number, screenY: number): void {
    if (this.hoverTimeout) {
      clearTimeout(this.hoverTimeout);
    }

    this.hoverTimeout = setTimeout(() => {
      this.handleHover(screenX, screenY);
    }, this.hoverDelay);
  }

  /**
   * Handle hover events
   */
  private handleHover(screenX: number, screenY: number): void {
    const worldPos = this.viewportManager.screenToWorld(screenX, screenY);
    const seat = this.dataManager.getSeatAtPoint(worldPos.x, worldPos.y, SEAT_SIZE / 2);

    // Update hover state
    this.dataManager.setSeatHover(seat?.id || null);

    // Update rendering if needed
    if (seat) {
      this.renderingEngine.updateSeat(seat);
    }
  }

  /**
   * Handle mouse leave
   */
  private handleMouseLeave(): void {
    this.dataManager.setSeatHover(null);
    this.isMouseDown = false;
    this.isDragging = false;
    
    if (this.hoverTimeout) {
      clearTimeout(this.hoverTimeout);
      this.hoverTimeout = null;
    }
  }

  /**
   * Handle panning
   */
  private handlePan(deltaX: number, deltaY: number): void {
    this.viewportManager.pan(deltaX, deltaY);
  }

  /**
   * Handle wheel events for zooming
   */
  private handleWheel(e: Konva.KonvaEventObject<WheelEvent>): void {
    e.evt.preventDefault();

    const pos = this.stage.getPointerPosition();
    if (!pos) return;

    const scaleBy = 1.05;
    const oldScale = this.viewportManager.getViewport().scale;
    
    let newScale;
    if (e.evt.deltaY > 0) {
      newScale = oldScale / scaleBy;
    } else {
      newScale = oldScale * scaleBy;
    }

    this.viewportManager.zoomAtPoint(newScale, pos.x, pos.y);
  }

  /**
   * Handle context menu
   */
  private handleContextMenu(e: Konva.KonvaEventObject<MouseEvent>): void {
    e.evt.preventDefault();
    
    const pos = this.stage.getPointerPosition();
    if (!pos) return;

    const worldPos = this.viewportManager.screenToWorld(pos.x, pos.y);
    const seat = this.dataManager.getSeatAtPoint(worldPos.x, worldPos.y, SEAT_SIZE / 2);

    if (seat) {
      // Emit context menu event for seat
      console.log('Context menu for seat:', seat.id);
    }
  }

  /**
   * Select seats in a rectangular area
   */
  selectSeatsInArea(startX: number, startY: number, endX: number, endY: number): void {
    const minX = Math.min(startX, endX);
    const maxX = Math.max(startX, endX);
    const minY = Math.min(startY, endY);
    const maxY = Math.max(startY, endY);

    const viewport = {
      x: minX,
      y: minY,
      width: maxX - minX,
      height: maxY - minY,
      scale: 1
    };

    const seatsInArea = this.dataManager.getSeatsInViewport(viewport);
    const activeSeatIds = seatsInArea
      .filter(seat => seat.isActive)
      .map(seat => seat.id);

    this.dataManager.selectSeats(activeSeatIds);
  }

  /**
   * Enable/disable interactions
   */
  setInteractionEnabled(enabled: boolean): void {
    this.stage.listening(enabled);
  }

  /**
   * Get seat at screen coordinates
   */
  getSeatAtScreenPosition(screenX: number, screenY: number): Seat | null {
    const worldPos = this.viewportManager.screenToWorld(screenX, screenY);
    return this.dataManager.getSeatAtPoint(worldPos.x, worldPos.y, SEAT_SIZE / 2);
  }

  /**
   * Programmatically select a seat
   */
  selectSeat(seatId: string, addToSelection: boolean = false): void {
    if (!addToSelection) {
      this.dataManager.clearSelection();
    }
    this.dataManager.toggleSeatSelection(seatId);
  }

  /**
   * Programmatically hover a seat
   */
  hoverSeat(seatId: string | null): void {
    this.dataManager.setSeatHover(seatId);
  }

  /**
   * Add event listener
   */
  addEventListener(listener: (event: SeatMapEvent) => void): void {
    this.eventListeners.push(listener);
  }

  /**
   * Remove event listener
   */
  removeEventListener(listener: (event: SeatMapEvent) => void): void {
    const index = this.eventListeners.indexOf(listener);
    if (index !== -1) {
      this.eventListeners.splice(index, 1);
    }
  }

  /**
   * Emit event to all listeners
   */
  private emitEvent(event: SeatMapEvent): void {
    this.eventListeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        console.error('Error in interaction event listener:', error);
      }
    });
  }

  /**
   * Clean up resources
   */
  destroy(): void {
    if (this.hoverTimeout) {
      clearTimeout(this.hoverTimeout);
    }
    this.eventListeners = [];
    this.stage.off();
  }
}
