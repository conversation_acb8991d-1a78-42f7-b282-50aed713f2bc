import { Seat, SeatSection } from './types';

/**
 * Flexible seat numbering and labeling system
 * Supports various numbering patterns and custom labeling schemes
 */

export interface NumberingPattern {
  type: 'numeric' | 'alphabetic' | 'alphanumeric' | 'custom';
  startValue?: number | string;
  increment?: number;
  prefix?: string;
  suffix?: string;
  reverse?: boolean;
  skipNumbers?: (number | string)[];
}

export interface RowNumberingConfig {
  pattern: NumberingPattern;
  direction: 'front-to-back' | 'back-to-front' | 'center-out';
}

export interface SeatNumberingConfig {
  pattern: NumberingPattern;
  direction: 'left-to-right' | 'right-to-left' | 'center-out';
  oddEvenSplit?: boolean; // For center-out numbering
}

export interface SectionNumberingConfig {
  sectionPrefix?: string;
  rowConfig: RowNumberingConfig;
  seatConfig: SeatNumberingConfig;
  customFormatter?: (section: string, row: string, seat: string) => string;
}

export class SeatNumberingSystem {
  
  /**
   * Apply numbering scheme to a section
   */
  static applySectionNumbering(
    section: SeatSection, 
    config: SectionNumberingConfig
  ): SeatSection {
    const updatedSeats = [...section.seats];
    
    // Group seats by row
    const seatsByRow = this.groupSeatsByRow(updatedSeats);
    const rowNumbers = Object.keys(seatsByRow).map(Number).sort((a, b) => a - b);
    
    // Generate row labels
    const rowLabels = this.generateRowLabels(rowNumbers, config.rowConfig);
    
    // Apply numbering to each row
    rowNumbers.forEach((rowNum, rowIndex) => {
      const seatsInRow = seatsByRow[rowNum];
      const rowLabel = rowLabels[rowIndex];
      
      // Generate seat labels for this row
      const seatLabels = this.generateSeatLabels(seatsInRow.length, config.seatConfig);
      
      // Apply labels to seats
      seatsInRow.forEach((seat, seatIndex) => {
        const seatLabel = seatLabels[seatIndex];
        const finalLabel = config.customFormatter 
          ? config.customFormatter(config.sectionPrefix || '', rowLabel, seatLabel)
          : this.formatSeatLabel(config.sectionPrefix, rowLabel, seatLabel);
        
        seat.label = finalLabel;
        seat.row = rowNum;
        seat.seatNumber = seatIndex + 1;
      });
    });

    return {
      ...section,
      seats: updatedSeats
    };
  }

  /**
   * Generate row labels based on configuration
   */
  private static generateRowLabels(
    rowNumbers: number[], 
    config: RowNumberingConfig
  ): string[] {
    const count = rowNumbers.length;
    let baseLabels = this.generateLabels(count, config.pattern);
    
    // Apply direction
    switch (config.direction) {
      case 'back-to-front':
        baseLabels = baseLabels.reverse();
        break;
      case 'center-out':
        baseLabels = this.applyCenterOutOrdering(baseLabels);
        break;
      // 'front-to-back' is default order
    }
    
    return baseLabels;
  }

  /**
   * Generate seat labels for a row
   */
  private static generateSeatLabels(
    seatCount: number, 
    config: SeatNumberingConfig
  ): string[] {
    let labels = this.generateLabels(seatCount, config.pattern);
    
    // Apply direction
    switch (config.direction) {
      case 'right-to-left':
        labels = labels.reverse();
        break;
      case 'center-out':
        if (config.oddEvenSplit) {
          labels = this.applyCenterOutOddEven(labels);
        } else {
          labels = this.applyCenterOutOrdering(labels);
        }
        break;
      // 'left-to-right' is default order
    }
    
    return labels;
  }

  /**
   * Generate labels based on pattern
   */
  private static generateLabels(count: number, pattern: NumberingPattern): string[] {
    const labels: string[] = [];
    
    switch (pattern.type) {
      case 'numeric':
        return this.generateNumericLabels(count, pattern);
      case 'alphabetic':
        return this.generateAlphabeticLabels(count, pattern);
      case 'alphanumeric':
        return this.generateAlphanumericLabels(count, pattern);
      case 'custom':
        return this.generateCustomLabels(count, pattern);
      default:
        return this.generateNumericLabels(count, pattern);
    }
  }

  /**
   * Generate numeric labels (1, 2, 3, ...)
   */
  private static generateNumericLabels(count: number, pattern: NumberingPattern): string[] {
    const labels: string[] = [];
    const start = typeof pattern.startValue === 'number' ? pattern.startValue : 1;
    const increment = pattern.increment || 1;
    const prefix = pattern.prefix || '';
    const suffix = pattern.suffix || '';
    const skipNumbers = new Set(pattern.skipNumbers || []);
    
    let current = start;
    let generated = 0;
    
    while (generated < count) {
      if (!skipNumbers.has(current)) {
        labels.push(`${prefix}${current}${suffix}`);
        generated++;
      }
      current += increment;
    }
    
    return pattern.reverse ? labels.reverse() : labels;
  }

  /**
   * Generate alphabetic labels (A, B, C, ...)
   */
  private static generateAlphabeticLabels(count: number, pattern: NumberingPattern): string[] {
    const labels: string[] = [];
    const prefix = pattern.prefix || '';
    const suffix = pattern.suffix || '';
    const skipLetters = new Set(pattern.skipNumbers || []);
    
    for (let i = 0; i < count; i++) {
      let letter = this.numberToLetter(i);
      
      // Skip specified letters
      while (skipLetters.has(letter)) {
        i++;
        letter = this.numberToLetter(i);
      }
      
      labels.push(`${prefix}${letter}${suffix}`);
    }
    
    return pattern.reverse ? labels.reverse() : labels;
  }

  /**
   * Generate alphanumeric labels (A1, A2, B1, B2, ...)
   */
  private static generateAlphanumericLabels(count: number, pattern: NumberingPattern): string[] {
    const labels: string[] = [];
    const prefix = pattern.prefix || '';
    const suffix = pattern.suffix || '';
    
    // For alphanumeric, we'll cycle through letters and numbers
    const lettersPerNumber = Math.ceil(Math.sqrt(count));
    
    for (let i = 0; i < count; i++) {
      const letterIndex = Math.floor(i / lettersPerNumber);
      const numberIndex = (i % lettersPerNumber) + 1;
      const letter = this.numberToLetter(letterIndex);
      
      labels.push(`${prefix}${letter}${numberIndex}${suffix}`);
    }
    
    return pattern.reverse ? labels.reverse() : labels;
  }

  /**
   * Generate custom labels based on pattern
   */
  private static generateCustomLabels(count: number, pattern: NumberingPattern): string[] {
    const labels: string[] = [];
    const prefix = pattern.prefix || '';
    const suffix = pattern.suffix || '';
    
    // For custom patterns, use the startValue as a template
    const template = pattern.startValue?.toString() || '1';
    
    for (let i = 0; i < count; i++) {
      labels.push(`${prefix}${template}${i + 1}${suffix}`);
    }
    
    return pattern.reverse ? labels.reverse() : labels;
  }

  /**
   * Apply center-out ordering
   */
  private static applyCenterOutOrdering(labels: string[]): string[] {
    const result: string[] = new Array(labels.length);
    const center = Math.floor(labels.length / 2);
    
    for (let i = 0; i < labels.length; i++) {
      if (i % 2 === 0) {
        // Even indices go to the right of center
        result[center + Math.floor(i / 2)] = labels[i];
      } else {
        // Odd indices go to the left of center
        result[center - Math.ceil(i / 2)] = labels[i];
      }
    }
    
    return result;
  }

  /**
   * Apply center-out ordering with odd/even split
   */
  private static applyCenterOutOddEven(labels: string[]): string[] {
    const result: string[] = new Array(labels.length);
    const center = Math.floor(labels.length / 2);
    
    let oddIndex = 0;
    let evenIndex = 0;
    
    for (let i = 0; i < labels.length; i++) {
      if (i < center) {
        // Left side gets odd numbers
        result[center - 1 - i] = labels[oddIndex];
        oddIndex += 2;
      } else {
        // Right side gets even numbers
        result[i] = labels[evenIndex + 1];
        evenIndex += 2;
      }
    }
    
    return result;
  }

  /**
   * Convert number to letter (0=A, 1=B, ..., 25=Z, 26=AA, ...)
   */
  private static numberToLetter(num: number): string {
    let result = '';
    while (num >= 0) {
      result = String.fromCharCode(65 + (num % 26)) + result;
      num = Math.floor(num / 26) - 1;
    }
    return result;
  }

  /**
   * Group seats by row number
   */
  private static groupSeatsByRow(seats: Seat[]): Record<number, Seat[]> {
    return seats.reduce((groups, seat) => {
      const row = seat.row;
      if (!groups[row]) {
        groups[row] = [];
      }
      groups[row].push(seat);
      return groups;
    }, {} as Record<number, Seat[]>);
  }

  /**
   * Format final seat label
   */
  private static formatSeatLabel(
    sectionPrefix: string | undefined, 
    rowLabel: string, 
    seatLabel: string
  ): string {
    if (sectionPrefix) {
      return `${sectionPrefix}-${rowLabel}-${seatLabel}`;
    }
    return `${rowLabel}-${seatLabel}`;
  }

  /**
   * Preset numbering configurations
   */
  static getPresetConfigs() {
    return {
      standard: {
        rowConfig: {
          pattern: { type: 'alphabetic' as const },
          direction: 'front-to-back' as const
        },
        seatConfig: {
          pattern: { type: 'numeric' as const, startValue: 1 },
          direction: 'left-to-right' as const
        }
      },
      
      theater: {
        rowConfig: {
          pattern: { type: 'alphabetic' as const },
          direction: 'front-to-back' as const
        },
        seatConfig: {
          pattern: { type: 'numeric' as const, startValue: 1 },
          direction: 'center-out' as const,
          oddEvenSplit: true
        }
      },
      
      stadium: {
        rowConfig: {
          pattern: { type: 'numeric' as const, startValue: 1 },
          direction: 'front-to-back' as const
        },
        seatConfig: {
          pattern: { type: 'numeric' as const, startValue: 1 },
          direction: 'left-to-right' as const
        }
      },
      
      concert: {
        rowConfig: {
          pattern: { type: 'alphabetic' as const },
          direction: 'back-to-front' as const
        },
        seatConfig: {
          pattern: { type: 'numeric' as const, startValue: 101 },
          direction: 'center-out' as const
        }
      }
    };
  }
}
