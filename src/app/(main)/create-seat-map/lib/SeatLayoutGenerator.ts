import { Seat, SeatSection, SectionShape, SectionStyle } from './types';
import { SEAT_SIZE, SEAT_GAP, SHAPE_PADDING } from './config';

/**
 * Generates seat layouts for different venue types and shapes
 * Supports rectangles, arcs, polygons, circles, and custom shapes
 */
export class SeatLayoutGenerator {
  
  /**
   * Generate a rectangular seated section
   */
  static generateRectangularSection(
    id: string,
    name: string,
    x: number,
    y: number,
    rows: number,
    seatsPerRow: number,
    style?: Partial<SectionStyle>
  ): SeatSection {
    const seats: Seat[] = [];
    const seatWidth = SEAT_SIZE + SEAT_GAP;
    const seatHeight = SEAT_SIZE + SEAT_GAP;
    
    for (let row = 0; row < rows; row++) {
      for (let seatNum = 0; seatNum < seatsPerRow; seatNum++) {
        const seatX = SHAPE_PADDING + seatNum * seatWidth;
        const seatY = SHAPE_PADDING + row * seatHeight;
        
        seats.push({
          id: `${id}-seat-${row + 1}-${seatNum + 1}`,
          x: seatX,
          y: seatY,
          row: row + 1,
          seatNumber: seatNum + 1,
          sectionId: id,
          isActive: true,
          isSelected: false,
          isHovered: false,
          label: `${row + 1}-${seatNum + 1}`
        });
      }
    }

    const width = seatsPerRow * seatWidth + SHAPE_PADDING * 2 - SEAT_GAP;
    const height = rows * seatHeight + SHAPE_PADDING * 2 - SEAT_GAP;

    return {
      id,
      name,
      type: 'seated',
      x,
      y,
      width,
      height,
      rotation: 0,
      seats,
      capacity: seats.length,
      shape: { type: 'rectangle' },
      style: this.getDefaultStyle(style)
    };
  }

  /**
   * Generate an arc-shaped seated section (like theater or stadium)
   */
  static generateArcSection(
    id: string,
    name: string,
    centerX: number,
    centerY: number,
    innerRadius: number,
    outerRadius: number,
    startAngle: number,
    endAngle: number,
    rows: number,
    style?: Partial<SectionStyle>
  ): SeatSection {
    const seats: Seat[] = [];
    const radiusStep = (outerRadius - innerRadius) / rows;
    
    let seatId = 1;
    
    for (let row = 0; row < rows; row++) {
      const radius = innerRadius + row * radiusStep;
      const circumference = radius * (endAngle - startAngle);
      const seatsInRow = Math.floor(circumference / (SEAT_SIZE + SEAT_GAP));
      const angleStep = (endAngle - startAngle) / seatsInRow;
      
      for (let seatNum = 0; seatNum < seatsInRow; seatNum++) {
        const angle = startAngle + seatNum * angleStep;
        const seatX = centerX + radius * Math.cos(angle) - SEAT_SIZE / 2;
        const seatY = centerY + radius * Math.sin(angle) - SEAT_SIZE / 2;
        
        seats.push({
          id: `${id}-seat-${row + 1}-${seatNum + 1}`,
          x: seatX,
          y: seatY,
          row: row + 1,
          seatNumber: seatNum + 1,
          sectionId: id,
          isActive: true,
          isSelected: false,
          isHovered: false,
          label: `${row + 1}-${seatNum + 1}`
        });
      }
    }

    // Calculate bounding box
    const bounds = this.calculateBounds(seats);

    return {
      id,
      name,
      type: 'seated',
      x: bounds.minX - SHAPE_PADDING,
      y: bounds.minY - SHAPE_PADDING,
      width: bounds.maxX - bounds.minX + SHAPE_PADDING * 2,
      height: bounds.maxY - bounds.minY + SHAPE_PADDING * 2,
      rotation: 0,
      seats,
      capacity: seats.length,
      shape: { 
        type: 'arc', 
        radius: (innerRadius + outerRadius) / 2,
        startAngle,
        endAngle
      },
      style: this.getDefaultStyle(style)
    };
  }

  /**
   * Generate a circular section (like round tables)
   */
  static generateCircularSection(
    id: string,
    name: string,
    centerX: number,
    centerY: number,
    radius: number,
    capacity: number,
    style?: Partial<SectionStyle>
  ): SeatSection {
    const seats: Seat[] = [];
    const angleStep = (2 * Math.PI) / capacity;
    
    for (let i = 0; i < capacity; i++) {
      const angle = i * angleStep;
      const seatX = centerX + radius * Math.cos(angle) - SEAT_SIZE / 2;
      const seatY = centerY + radius * Math.sin(angle) - SEAT_SIZE / 2;
      
      seats.push({
        id: `${id}-seat-${i + 1}`,
        x: seatX,
        y: seatY,
        row: 1,
        seatNumber: i + 1,
        sectionId: id,
        isActive: true,
        isSelected: false,
        isHovered: false,
        label: `${i + 1}`
      });
    }

    const diameter = (radius + SEAT_SIZE / 2) * 2;

    return {
      id,
      name,
      type: 'table-round',
      x: centerX - diameter / 2,
      y: centerY - diameter / 2,
      width: diameter,
      height: diameter,
      rotation: 0,
      seats,
      capacity: seats.length,
      shape: { type: 'circle', radius },
      style: this.getDefaultStyle(style)
    };
  }

  /**
   * Generate a polygon-shaped section
   */
  static generatePolygonSection(
    id: string,
    name: string,
    points: number[],
    capacity: number,
    style?: Partial<SectionStyle>
  ): SeatSection {
    const seats: Seat[] = [];
    const bounds = this.getPolygonBounds(points);
    
    // Generate seats within the polygon using point-in-polygon algorithm
    const seatSpacing = SEAT_SIZE + SEAT_GAP;
    const cols = Math.floor(bounds.width / seatSpacing);
    const rows = Math.floor(bounds.height / seatSpacing);
    
    let seatCount = 0;
    
    for (let row = 0; row < rows && seatCount < capacity; row++) {
      for (let col = 0; col < cols && seatCount < capacity; col++) {
        const seatX = bounds.minX + col * seatSpacing + seatSpacing / 2;
        const seatY = bounds.minY + row * seatSpacing + seatSpacing / 2;
        
        if (this.isPointInPolygon(seatX, seatY, points)) {
          seats.push({
            id: `${id}-seat-${seatCount + 1}`,
            x: seatX - SEAT_SIZE / 2,
            y: seatY - SEAT_SIZE / 2,
            row: row + 1,
            seatNumber: col + 1,
            sectionId: id,
            isActive: true,
            isSelected: false,
            isHovered: false,
            label: `${row + 1}-${col + 1}`
          });
          seatCount++;
        }
      }
    }

    return {
      id,
      name,
      type: 'seated',
      x: bounds.minX - SHAPE_PADDING,
      y: bounds.minY - SHAPE_PADDING,
      width: bounds.width + SHAPE_PADDING * 2,
      height: bounds.height + SHAPE_PADDING * 2,
      rotation: 0,
      seats,
      capacity: seats.length,
      shape: { type: 'polygon', points },
      style: this.getDefaultStyle(style)
    };
  }

  /**
   * Generate a standing section (no individual seats)
   */
  static generateStandingSection(
    id: string,
    name: string,
    x: number,
    y: number,
    width: number,
    height: number,
    capacity: number,
    style?: Partial<SectionStyle>
  ): SeatSection {
    return {
      id,
      name,
      type: 'standing',
      x,
      y,
      width,
      height,
      rotation: 0,
      seats: [], // No individual seats for standing sections
      capacity,
      shape: { type: 'rectangle' },
      style: this.getDefaultStyle(style)
    };
  }

  /**
   * Generate a rectangular table section
   */
  static generateRectangleTableSection(
    id: string,
    name: string,
    x: number,
    y: number,
    width: number,
    height: number,
    seatsPerSide: number,
    style?: Partial<SectionStyle>
  ): SeatSection {
    const seats: Seat[] = [];
    const perimeter = 2 * (width + height);
    const seatSpacing = perimeter / (seatsPerSide * 4);
    
    let seatId = 1;
    
    // Top side
    for (let i = 0; i < seatsPerSide; i++) {
      const seatX = x + (i + 0.5) * (width / seatsPerSide) - SEAT_SIZE / 2;
      const seatY = y - SEAT_SIZE;
      
      seats.push({
        id: `${id}-seat-${seatId++}`,
        x: seatX,
        y: seatY,
        row: 1,
        seatNumber: i + 1,
        sectionId: id,
        isActive: true,
        isSelected: false,
        isHovered: false,
        label: `T${i + 1}`
      });
    }
    
    // Right side
    for (let i = 0; i < seatsPerSide; i++) {
      const seatX = x + width;
      const seatY = y + (i + 0.5) * (height / seatsPerSide) - SEAT_SIZE / 2;
      
      seats.push({
        id: `${id}-seat-${seatId++}`,
        x: seatX,
        y: seatY,
        row: 2,
        seatNumber: i + 1,
        sectionId: id,
        isActive: true,
        isSelected: false,
        isHovered: false,
        label: `R${i + 1}`
      });
    }
    
    // Bottom side
    for (let i = 0; i < seatsPerSide; i++) {
      const seatX = x + width - (i + 0.5) * (width / seatsPerSide) - SEAT_SIZE / 2;
      const seatY = y + height;
      
      seats.push({
        id: `${id}-seat-${seatId++}`,
        x: seatX,
        y: seatY,
        row: 3,
        seatNumber: i + 1,
        sectionId: id,
        isActive: true,
        isSelected: false,
        isHovered: false,
        label: `B${i + 1}`
      });
    }
    
    // Left side
    for (let i = 0; i < seatsPerSide; i++) {
      const seatX = x - SEAT_SIZE;
      const seatY = y + height - (i + 0.5) * (height / seatsPerSide) - SEAT_SIZE / 2;
      
      seats.push({
        id: `${id}-seat-${seatId++}`,
        x: seatX,
        y: seatY,
        row: 4,
        seatNumber: i + 1,
        sectionId: id,
        isActive: true,
        isSelected: false,
        isHovered: false,
        label: `L${i + 1}`
      });
    }

    return {
      id,
      name,
      type: 'table-rectangle',
      x: x - SEAT_SIZE - SHAPE_PADDING,
      y: y - SEAT_SIZE - SHAPE_PADDING,
      width: width + SEAT_SIZE * 2 + SHAPE_PADDING * 2,
      height: height + SEAT_SIZE * 2 + SHAPE_PADDING * 2,
      rotation: 0,
      seats,
      capacity: seats.length,
      shape: { type: 'rectangle' },
      style: this.getDefaultStyle(style)
    };
  }

  // Helper methods

  private static getDefaultStyle(customStyle?: Partial<SectionStyle>): SectionStyle {
    return {
      fillColor: 'rgba(79, 70, 229, 0.1)',
      strokeColor: '#4F46E5',
      strokeWidth: 2,
      seatColor: '#4F46E5',
      selectedSeatColor: '#10B981',
      inactiveSeatColor: '#9CA3AF',
      textColor: '#1F2937',
      fontSize: 12,
      ...customStyle
    };
  }

  private static calculateBounds(seats: Seat[]) {
    if (seats.length === 0) {
      return { minX: 0, minY: 0, maxX: 0, maxY: 0 };
    }

    let minX = seats[0].x;
    let minY = seats[0].y;
    let maxX = seats[0].x + SEAT_SIZE;
    let maxY = seats[0].y + SEAT_SIZE;

    seats.forEach(seat => {
      minX = Math.min(minX, seat.x);
      minY = Math.min(minY, seat.y);
      maxX = Math.max(maxX, seat.x + SEAT_SIZE);
      maxY = Math.max(maxY, seat.y + SEAT_SIZE);
    });

    return { minX, minY, maxX, maxY };
  }

  private static getPolygonBounds(points: number[]) {
    let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;

    for (let i = 0; i < points.length; i += 2) {
      const x = points[i];
      const y = points[i + 1];
      minX = Math.min(minX, x);
      maxX = Math.max(maxX, x);
      minY = Math.min(minY, y);
      maxY = Math.max(maxY, y);
    }

    return {
      minX,
      minY,
      maxX,
      maxY,
      width: maxX - minX,
      height: maxY - minY
    };
  }

  private static isPointInPolygon(x: number, y: number, points: number[]): boolean {
    let inside = false;
    
    for (let i = 0, j = points.length - 2; i < points.length; j = i, i += 2) {
      const xi = points[i];
      const yi = points[i + 1];
      const xj = points[j];
      const yj = points[j + 1];
      
      if (((yi > y) !== (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi)) {
        inside = !inside;
      }
    }
    
    return inside;
  }
}
