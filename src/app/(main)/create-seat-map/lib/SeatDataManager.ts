import { Seat, SeatSection, SeatMapEvent, SeatState } from './types';
import { SpatialIndex } from './SpatialIndex';

/**
 * Manages seat data with efficient state updates and event handling
 * Optimized for large datasets with minimal memory overhead
 */
export class SeatDataManager {
  private seats: Map<string, Seat> = new Map();
  private sections: Map<string, SeatSection> = new Map();
  private spatialIndex: SpatialIndex;
  private eventListeners: ((event: SeatMapEvent) => void)[] = [];
  private selectedSeats: Set<string> = new Set();
  private hoveredSeat: string | null = null;
  private isDirty: boolean = false;

  constructor() {
    this.spatialIndex = new SpatialIndex();
  }

  /**
   * Add a new seat section with generated seats
   */
  addSection(section: SeatSection): void {
    this.sections.set(section.id, section);
    this.spatialIndex.addSection(section);

    // Add all seats in the section
    section.seats.forEach(seat => {
      this.seats.set(seat.id, seat);
      this.spatialIndex.addSeat(seat);
    });

    this.markDirty();
  }

  /**
   * Remove a seat section and all its seats
   */
  removeSection(sectionId: string): void {
    const section = this.sections.get(sectionId);
    if (!section) return;

    // Remove all seats in the section
    section.seats.forEach(seat => {
      this.seats.delete(seat.id);
      this.spatialIndex.removeSeat(seat.id);
      this.selectedSeats.delete(seat.id);
    });

    this.sections.delete(sectionId);
    this.spatialIndex.removeSection(sectionId);
    this.markDirty();
  }

  /**
   * Update seat state efficiently
   */
  updateSeatState(seatId: string, state: Partial<Seat>): void {
    const seat = this.seats.get(seatId);
    if (!seat) return;

    const oldState = { ...seat };
    Object.assign(seat, state);

    // Handle selection state changes
    if (state.isSelected !== undefined) {
      if (state.isSelected) {
        this.selectedSeats.add(seatId);
        this.emitEvent({ type: 'seat-selected', seat });
      } else {
        this.selectedSeats.delete(seatId);
        this.emitEvent({ type: 'seat-deselected', seat });
      }
    }

    // Handle hover state changes
    if (state.isHovered !== undefined) {
      if (state.isHovered) {
        this.hoveredSeat = seatId;
        this.emitEvent({ type: 'seat-hovered', seat });
      } else if (this.hoveredSeat === seatId) {
        this.hoveredSeat = null;
        this.emitEvent({ type: 'seat-unhovered', seat });
      }
    }

    this.markDirty();
  }

  /**
   * Batch update multiple seats for performance
   */
  batchUpdateSeats(updates: { seatId: string; state: Partial<Seat> }[]): void {
    updates.forEach(({ seatId, state }) => {
      const seat = this.seats.get(seatId);
      if (seat) {
        Object.assign(seat, state);
      }
    });
    this.markDirty();
  }

  /**
   * Toggle seat selection
   */
  toggleSeatSelection(seatId: string): void {
    const seat = this.seats.get(seatId);
    if (!seat || !seat.isActive) return;

    this.updateSeatState(seatId, { isSelected: !seat.isSelected });
  }

  /**
   * Select multiple seats
   */
  selectSeats(seatIds: string[]): void {
    const updates = seatIds
      .filter(id => {
        const seat = this.seats.get(id);
        return seat && seat.isActive && !seat.isSelected;
      })
      .map(seatId => ({ seatId, state: { isSelected: true } }));

    this.batchUpdateSeats(updates);
  }

  /**
   * Deselect all seats
   */
  clearSelection(): void {
    const updates = Array.from(this.selectedSeats).map(seatId => ({
      seatId,
      state: { isSelected: false }
    }));

    this.batchUpdateSeats(updates);
    this.selectedSeats.clear();
  }

  /**
   * Set hover state for a seat
   */
  setSeatHover(seatId: string | null): void {
    // Clear previous hover
    if (this.hoveredSeat && this.hoveredSeat !== seatId) {
      this.updateSeatState(this.hoveredSeat, { isHovered: false });
    }

    // Set new hover
    if (seatId) {
      this.updateSeatState(seatId, { isHovered: true });
    }
  }

  /**
   * Get seat by ID
   */
  getSeat(seatId: string): Seat | undefined {
    return this.seats.get(seatId);
  }

  /**
   * Get section by ID
   */
  getSection(sectionId: string): SeatSection | undefined {
    return this.sections.get(sectionId);
  }

  /**
   * Get all seats
   */
  getAllSeats(): Seat[] {
    return Array.from(this.seats.values());
  }

  /**
   * Get all sections
   */
  getAllSections(): SeatSection[] {
    return Array.from(this.sections.values());
  }

  /**
   * Get selected seats
   */
  getSelectedSeats(): Seat[] {
    return Array.from(this.selectedSeats)
      .map(id => this.seats.get(id))
      .filter(Boolean) as Seat[];
  }

  /**
   * Get seats in viewport using spatial index
   */
  getSeatsInViewport(viewport: { x: number; y: number; width: number; height: number; scale: number }, padding: number = 0): Seat[] {
    return this.spatialIndex.getSeatsInViewport(viewport, padding);
  }

  /**
   * Get sections in viewport using spatial index
   */
  getSectionsInViewport(viewport: { x: number; y: number; width: number; height: number; scale: number }, padding: number = 0): SeatSection[] {
    return this.spatialIndex.getSectionsInViewport(viewport, padding);
  }

  /**
   * Find seat at specific coordinates
   */
  getSeatAtPoint(x: number, y: number, tolerance: number = 10): Seat | null {
    return this.spatialIndex.getSeatAtPoint(x, y, tolerance);
  }

  /**
   * Get performance statistics
   */
  getStats() {
    return {
      totalSeats: this.seats.size,
      totalSections: this.sections.size,
      selectedSeats: this.selectedSeats.size,
      hoveredSeat: this.hoveredSeat,
      isDirty: this.isDirty,
      spatialIndex: this.spatialIndex.getStats()
    };
  }

  /**
   * Check if data needs re-rendering
   */
  isDirtyData(): boolean {
    return this.isDirty;
  }

  /**
   * Mark data as clean after rendering
   */
  markClean(): void {
    this.isDirty = false;
  }

  /**
   * Add event listener
   */
  addEventListener(listener: (event: SeatMapEvent) => void): void {
    this.eventListeners.push(listener);
  }

  /**
   * Remove event listener
   */
  removeEventListener(listener: (event: SeatMapEvent) => void): void {
    const index = this.eventListeners.indexOf(listener);
    if (index !== -1) {
      this.eventListeners.splice(index, 1);
    }
  }

  /**
   * Clear all data
   */
  clear(): void {
    this.seats.clear();
    this.sections.clear();
    this.selectedSeats.clear();
    this.hoveredSeat = null;
    this.spatialIndex.clear();
    this.markDirty();
  }

  // Private methods

  private markDirty(): void {
    this.isDirty = true;
  }

  private emitEvent(event: SeatMapEvent): void {
    this.eventListeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        console.error('Error in seat map event listener:', error);
      }
    });
  }
}
