import Konva from 'konva';
import { Seat, SeatSection, SeatMapConfig, SeatMapEvent, PerformanceMetrics } from './types';
import { SeatDataManager } from './SeatDataManager';
import { ViewportManager } from './ViewportManager';
import { VirtualizationEngine } from './VirtualizationEngine';
import { SeatRenderingEngine } from './SeatRenderingEngine';
import { InteractionManager } from './InteractionManager';
import { PerformanceMonitor } from './PerformanceMonitor';
import {
  VIEWPORT_PADDING,
  LOD_THRESHOLD_1,
  LOD_THRESHOLD_2,
  MAX_SEATS_PER_FRAME,
  SPATIAL_INDEX_CELL_SIZE
} from './config';

/**
 * Main seat map engine that orchestrates all components for high-performance rendering
 * Handles up to 100,000+ seats with smooth interactions
 */
export class SeatMapEngine {
  private stage: Konva.Stage;
  private layer: Konva.Layer;
  private dataManager: SeatDataManager;
  private viewportManager: ViewportManager;
  private virtualizationEngine: VirtualizationEngine;
  private renderingEngine: SeatRenderingEngine;
  private interactionManager: InteractionManager;
  private performanceMonitor: PerformanceMonitor;

  private config: SeatMapConfig;
  private animationFrameId: number | null = null;
  private isDestroyed: boolean = false;
  private eventListeners: ((event: SeatMapEvent) => void)[] = [];
  private lastPerformanceEmit: number = 0;
  private performanceEmitInterval: number = 1000; // Emit performance events every 1 second
  private lastRenderLog: number = 0;
  private renderLogInterval: number = 2000; // Log render info every 2 seconds

  constructor(container: HTMLDivElement, config?: Partial<SeatMapConfig>) {
    // Setup default configuration
    this.config = {
      maxSeatsPerFrame: MAX_SEATS_PER_FRAME,
      viewportPadding: VIEWPORT_PADDING,
      lodThresholds: [LOD_THRESHOLD_1, LOD_THRESHOLD_2],
      spatialIndexCellSize: SPATIAL_INDEX_CELL_SIZE,
      enableCaching: true,
      enableVirtualization: true,
      ...config
    };

    // Initialize Konva stage and layer
    this.stage = new Konva.Stage({
      container,
      width: container.clientWidth,
      height: container.clientHeight,
      draggable: false // We handle dragging manually for better performance
    });

    this.layer = new Konva.Layer();
    this.stage.add(this.layer);

    // Initialize managers
    this.dataManager = new SeatDataManager();
    this.viewportManager = new ViewportManager({
      x: 0,
      y: 0,
      width: this.stage.width(),
      height: this.stage.height(),
      scale: 1
    });

    console.log('SeatMapEngine: Viewport initialized', {
      width: this.stage.width(),
      height: this.stage.height()
    });

    this.virtualizationEngine = new VirtualizationEngine(
      this.dataManager,
      this.viewportManager
    );

    this.renderingEngine = new SeatRenderingEngine(this.layer);

    this.interactionManager = new InteractionManager(
      this.stage,
      this.dataManager,
      this.viewportManager,
      this.renderingEngine
    );

    this.performanceMonitor = new PerformanceMonitor();

    this.setupEventListeners();
    this.startRenderLoop();
  }

  /**
   * Add a seat section to the map
   */
  addSection(section: SeatSection): void {
    console.log('SeatMapEngine: Adding section', section.name, 'with', section.seats.length, 'seats');
    this.dataManager.addSection(section);
    this.forceUpdate();
    console.log('SeatMapEngine: Section added, total sections:', this.dataManager.getAllSections().length);
  }

  /**
   * Remove a seat section from the map
   */
  removeSection(sectionId: string): void {
    this.dataManager.removeSection(sectionId);
  }

  /**
   * Update seat state
   */
  updateSeat(seatId: string, updates: Partial<Seat>): void {
    this.dataManager.updateSeatState(seatId, updates);
  }

  /**
   * Get seat by ID
   */
  getSeat(seatId: string): Seat | undefined {
    return this.dataManager.getSeat(seatId);
  }

  /**
   * Get all selected seats
   */
  getSelectedSeats(): Seat[] {
    return this.dataManager.getSelectedSeats();
  }

  /**
   * Clear all selections
   */
  clearSelection(): void {
    this.dataManager.clearSelection();
  }

  /**
   * Zoom to fit all content
   */
  zoomToFit(padding: number = 50): void {
    const stats = this.dataManager.getStats();
    if (stats.spatialIndex.bounds) {
      this.viewportManager.zoomToFit(stats.spatialIndex.bounds, padding);
    }
  }

  /**
   * Set zoom level
   */
  setZoom(scale: number): void {
    this.viewportManager.setScale(scale);
  }

  /**
   * Center on specific coordinates
   */
  centerOn(x: number, y: number): void {
    this.viewportManager.centerOn(x, y);
  }

  /**
   * Resize the stage
   */
  resize(width: number, height: number): void {
    this.stage.width(width);
    this.stage.height(height);
    this.viewportManager.setDimensions(width, height);
  }

  /**
   * Get performance metrics
   */
  getPerformanceMetrics(): PerformanceMetrics {
    return this.virtualizationEngine.getPerformanceMetrics();
  }

  /**
   * Get detailed performance statistics
   */
  getPerformanceStats() {
    return this.performanceMonitor.getCurrentStats();
  }

  /**
   * Get performance trend data
   */
  getPerformanceTrend() {
    return this.performanceMonitor.getPerformanceTrend();
  }

  /**
   * Get current statistics
   */
  getStats() {
    return {
      data: this.dataManager.getStats(),
      viewport: this.viewportManager.getViewport(),
      virtualization: this.virtualizationEngine.getLODInfo(),
      rendering: this.renderingEngine.getStats(),
      performance: this.getPerformanceMetrics()
    };
  }

  /**
   * Enable or disable interactions
   */
  setInteractionEnabled(enabled: boolean): void {
    this.interactionManager.setInteractionEnabled(enabled);
  }

  /**
   * Add event listener
   */
  addEventListener(listener: (event: SeatMapEvent) => void): void {
    this.eventListeners.push(listener);
    this.dataManager.addEventListener(listener);
    this.viewportManager.addEventListener(listener);
    this.interactionManager.addEventListener(listener);
  }

  /**
   * Remove event listener
   */
  removeEventListener(listener: (event: SeatMapEvent) => void): void {
    const index = this.eventListeners.indexOf(listener);
    if (index !== -1) {
      this.eventListeners.splice(index, 1);
    }
    this.dataManager.removeEventListener(listener);
    this.viewportManager.removeEventListener(listener);
    this.interactionManager.removeEventListener(listener);
  }

  /**
   * Force a render update
   */
  forceUpdate(): void {
    this.virtualizationEngine.forceUpdate();
  }

  /**
   * Clear all data
   */
  clear(): void {
    this.dataManager.clear();
    this.renderingEngine.invalidateCache();
  }



  /**
   * Destroy the engine and clean up resources
   */
  destroy(): void {
    this.isDestroyed = true;

    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
    }

    this.interactionManager.destroy();
    this.renderingEngine.destroy();
    this.performanceMonitor.destroy();
    this.stage.destroy();
    this.eventListeners = [];
  }

  /**
   * Setup event listeners for cross-component communication
   */
  private setupEventListeners(): void {
    // Listen for viewport changes to trigger re-render
    this.viewportManager.addEventListener((event) => {
      if (event.type === 'viewport-changed') {
        this.virtualizationEngine.forceUpdate();
      }
    });

    // Performance monitoring is handled in the render loop
  }

  /**
   * Main render loop
   */
  private startRenderLoop(): void {
    const render = () => {
      if (this.isDestroyed) return;

      try {
        const frameStart = performance.now();

        // Update render context based on current state
        const renderContext = this.virtualizationEngine.updateRenderContext();

        // Always render if we have visible seats - force rendering to work
        if (renderContext.visibleSeats.length > 0 || renderContext.visibleSections.length > 0) {
          console.log('SeatMapEngine: Force rendering with', renderContext.visibleSeats.length, 'visible seats');
          this.renderingEngine.render(renderContext);
        }

        // Record performance metrics
        const frameEnd = performance.now();
        const metrics = this.virtualizationEngine.getPerformanceMetrics();

        // Update metrics with actual render time
        const updatedMetrics = {
          ...metrics,
          renderTime: frameEnd - frameStart
        };

        // Record in performance monitor
        this.performanceMonitor.recordMetric(updatedMetrics);

        // Emit performance metrics periodically (not every frame)
        const now = Date.now();
        if (now - this.lastPerformanceEmit > this.performanceEmitInterval) {
          this.emitEvent({
            type: 'performance-update',
            metrics: updatedMetrics
          });
          this.lastPerformanceEmit = now;
        }

      } catch (error) {
        console.error('Error in render loop:', error);
      }

      this.animationFrameId = requestAnimationFrame(render);
    };

    this.animationFrameId = requestAnimationFrame(render);
  }

  /**
   * Emit event to all listeners
   */
  private emitEvent(event: SeatMapEvent): void {
    this.eventListeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        console.error('Error in seat map event listener:', error);
      }
    });
  }
}
