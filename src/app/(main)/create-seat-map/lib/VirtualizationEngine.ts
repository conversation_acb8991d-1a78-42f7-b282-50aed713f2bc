import { Seat, SeatSection, Viewport, RenderContext, PerformanceMetrics } from './types';
import { SeatDataManager } from './SeatDataManager';
import { ViewportManager } from './ViewportManager';
import { VIEWPORT_PADDING, MAX_SEATS_PER_FRAME } from './config';

/**
 * High-performance virtualization engine that only renders visible content
 * Implements level-of-detail rendering and viewport culling
 */
export class VirtualizationEngine {
  private dataManager: SeatDataManager;
  private viewportManager: ViewportManager;
  private renderContext: RenderContext;
  private performanceMetrics: PerformanceMetrics;
  private lastRenderTime: number = 0;
  private frameCount: number = 0;

  constructor(dataManager: SeatDataManager, viewportManager: ViewportManager) {
    this.dataManager = dataManager;
    this.viewportManager = viewportManager;
    
    this.renderContext = {
      viewport: viewportManager.getViewport(),
      lodLevel: 0,
      visibleSeats: [],
      visibleSections: [],
      isDirty: true
    };

    this.performanceMetrics = {
      renderTime: 0,
      seatCount: 0,
      visibleSeatCount: 0,
      frameRate: 0,
      memoryUsage: 0
    };
  }

  /**
   * Update the render context based on current viewport and data
   */
  updateRenderContext(): RenderContext {
    const startTime = performance.now();
    
    const viewport = this.viewportManager.getViewport();
    const lodLevel = this.viewportManager.getLODLevel();
    
    // Check if we need to update the render context
    const needsUpdate = this.viewportManager.isDirtyViewport() || 
                       this.dataManager.isDirtyData() ||
                       this.renderContext.lodLevel !== lodLevel;

    if (!needsUpdate) {
      return this.renderContext;
    }

    // Update viewport and LOD level
    this.renderContext.viewport = viewport;
    this.renderContext.lodLevel = lodLevel;

    // Get visible content based on viewport
    this.renderContext.visibleSections = this.dataManager.getSectionsInViewport(
      viewport,
      VIEWPORT_PADDING
    );

    console.log('VirtualizationEngine: Found', this.renderContext.visibleSections.length, 'visible sections');

    // Get visible seats with LOD-based filtering
    const allVisibleSeats = this.dataManager.getSeatsInViewport(
      viewport,
      VIEWPORT_PADDING
    );

    console.log('VirtualizationEngine: Found', allVisibleSeats.length, 'visible seats before LOD filtering');

    this.renderContext.visibleSeats = this.applyLODFiltering(
      allVisibleSeats, 
      lodLevel, 
      viewport
    );

    // Limit seats per frame for performance
    if (this.renderContext.visibleSeats.length > MAX_SEATS_PER_FRAME) {
      this.renderContext.visibleSeats = this.prioritizeSeats(
        this.renderContext.visibleSeats, 
        viewport
      );
    }

    this.renderContext.isDirty = false;

    // Update performance metrics
    const endTime = performance.now();
    this.updatePerformanceMetrics(endTime - startTime);

    // Mark managers as clean
    this.viewportManager.markClean();
    this.dataManager.markClean();

    return this.renderContext;
  }

  /**
   * Get the current render context
   */
  getRenderContext(): RenderContext {
    return { ...this.renderContext };
  }

  /**
   * Apply level-of-detail filtering to seats
   */
  private applyLODFiltering(seats: Seat[], lodLevel: number, viewport: Viewport): Seat[] {
    switch (lodLevel) {
      case 0: // Full detail - render all seats
        return seats;
        
      case 1: // Simplified - skip some seats based on density
        return this.applySpatialThinning(seats, viewport.scale);
        
      case 2: // Density only - render very few representative seats
        return this.applyDensityThinning(seats, viewport.scale);
        
      default:
        return seats;
    }
  }

  /**
   * Apply spatial thinning for simplified LOD
   */
  private applySpatialThinning(seats: Seat[], scale: number): Seat[] {
    // Skip seats based on scale - lower scale means more aggressive thinning
    const skipFactor = Math.max(1, Math.floor(1 / scale));
    
    return seats.filter((seat, index) => {
      // Always include selected or hovered seats
      if (seat.isSelected || seat.isHovered) return true;
      
      // Skip seats based on their position to create a regular pattern
      const gridX = Math.floor(seat.x / 50);
      const gridY = Math.floor(seat.y / 50);
      return (gridX + gridY) % skipFactor === 0;
    });
  }

  /**
   * Apply density thinning for density-only LOD
   */
  private applyDensityThinning(seats: Seat[], scale: number): Seat[] {
    // Very aggressive thinning - only show a small representative sample
    const maxSeats = Math.max(100, seats.length * scale * 0.1);
    
    const selectedSeats = seats.filter(seat => seat.isSelected || seat.isHovered);
    const regularSeats = seats.filter(seat => !seat.isSelected && !seat.isHovered);
    
    // Always include selected/hovered seats
    const result = [...selectedSeats];
    
    // Add a regular grid of representative seats
    const step = Math.max(1, Math.floor(regularSeats.length / maxSeats));
    for (let i = 0; i < regularSeats.length; i += step) {
      result.push(regularSeats[i]);
      if (result.length >= maxSeats) break;
    }
    
    return result;
  }

  /**
   * Prioritize seats when we hit the render limit
   */
  private prioritizeSeats(seats: Seat[], viewport: Viewport): Seat[] {
    const viewportCenter = {
      x: viewport.x + viewport.width / 2,
      y: viewport.y + viewport.height / 2
    };

    // Sort seats by priority
    const prioritizedSeats = seats.sort((a, b) => {
      // Highest priority: selected seats
      if (a.isSelected && !b.isSelected) return -1;
      if (!a.isSelected && b.isSelected) return 1;
      
      // Second priority: hovered seats
      if (a.isHovered && !b.isHovered) return -1;
      if (!a.isHovered && b.isHovered) return 1;
      
      // Third priority: distance from viewport center
      const distA = Math.sqrt(
        Math.pow(a.x - viewportCenter.x, 2) + Math.pow(a.y - viewportCenter.y, 2)
      );
      const distB = Math.sqrt(
        Math.pow(b.x - viewportCenter.x, 2) + Math.pow(b.y - viewportCenter.y, 2)
      );
      
      return distA - distB;
    });

    return prioritizedSeats.slice(0, MAX_SEATS_PER_FRAME);
  }

  /**
   * Update performance metrics
   */
  private updatePerformanceMetrics(renderTime: number): void {
    this.performanceMetrics.renderTime = renderTime;
    this.performanceMetrics.seatCount = this.dataManager.getAllSeats().length;
    this.performanceMetrics.visibleSeatCount = this.renderContext.visibleSeats.length;

    // Calculate frame rate more accurately
    const now = performance.now();
    if (this.lastRenderTime > 0) {
      const deltaTime = now - this.lastRenderTime;
      if (deltaTime > 0) {
        // Use exponential moving average for smoother frame rate
        const instantFPS = 1000 / deltaTime;
        this.performanceMetrics.frameRate = this.performanceMetrics.frameRate === 0
          ? instantFPS
          : this.performanceMetrics.frameRate * 0.9 + instantFPS * 0.1;
      }
    } else {
      this.performanceMetrics.frameRate = 60; // Default to 60 FPS initially
    }
    this.lastRenderTime = now;

    // Estimate memory usage (rough calculation)
    this.performanceMetrics.memoryUsage = this.estimateMemoryUsage();

    this.frameCount++;
  }

  /**
   * Estimate memory usage
   */
  private estimateMemoryUsage(): number {
    const seatSize = 200; // Rough estimate of bytes per seat object
    const sectionSize = 500; // Rough estimate of bytes per section object
    
    return (this.performanceMetrics.seatCount * seatSize + 
            this.dataManager.getAllSections().length * sectionSize) / 1024 / 1024; // MB
  }

  /**
   * Get performance metrics
   */
  getPerformanceMetrics(): PerformanceMetrics {
    return { ...this.performanceMetrics };
  }

  /**
   * Check if content needs re-rendering
   */
  needsRender(): boolean {
    return this.viewportManager.isDirtyViewport() || 
           this.dataManager.isDirtyData() ||
           this.renderContext.isDirty;
  }

  /**
   * Force a render update
   */
  forceUpdate(): void {
    this.renderContext.isDirty = true;
  }

  /**
   * Get visible bounds for debugging
   */
  getVisibleBounds(): { minX: number; minY: number; maxX: number; maxY: number } {
    return this.viewportManager.getWorldBounds();
  }

  /**
   * Get LOD information for debugging
   */
  getLODInfo(): { level: number; description: string; seatCount: number } {
    const lodLevel = this.renderContext.lodLevel;
    const descriptions = [
      'Full Detail',
      'Simplified',
      'Density Only'
    ];

    return {
      level: lodLevel,
      description: descriptions[lodLevel] || 'Unknown',
      seatCount: this.renderContext.visibleSeats.length
    };
  }
}
