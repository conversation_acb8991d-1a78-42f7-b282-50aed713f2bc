import { Seat, SeatSection, SpatialBounds, SpatialIndexCell, Viewport } from './types';
import { SPATIAL_INDEX_CELL_SIZE } from './config';

/**
 * High-performance spatial index for fast seat and section lookups
 * Uses a grid-based approach for O(1) spatial queries
 */
export class SpatialIndex {
  private grid: Map<string, SpatialIndexCell> = new Map();
  private cellSize: number;
  private bounds: SpatialBounds = { minX: 0, minY: 0, maxX: 0, maxY: 0 };

  constructor(cellSize: number = SPATIAL_INDEX_CELL_SIZE) {
    this.cellSize = cellSize;
  }

  /**
   * Add a seat to the spatial index
   */
  addSeat(seat: Seat): void {
    const cellKey = this.getCellKey(seat.x, seat.y);
    let cell = this.grid.get(cellKey);
    
    if (!cell) {
      cell = this.createCell(seat.x, seat.y);
      this.grid.set(cellKey, cell);
    }
    
    cell.seats.push(seat);
    this.updateBounds(seat.x, seat.y);
  }

  /**
   * Add a section to the spatial index
   */
  addSection(section: SeatSection): void {
    const cells = this.getCellsForBounds({
      minX: section.x,
      minY: section.y,
      maxX: section.x + section.width,
      maxY: section.y + section.height
    });

    cells.forEach(cellKey => {
      let cell = this.grid.get(cellKey);
      if (!cell) {
        const [x, y] = this.parseCellKey(cellKey);
        cell = this.createCell(x * this.cellSize, y * this.cellSize);
        this.grid.set(cellKey, cell);
      }
      cell.sections.push(section);
    });

    this.updateBounds(section.x, section.y);
    this.updateBounds(section.x + section.width, section.y + section.height);
  }

  /**
   * Remove a seat from the spatial index
   */
  removeSeat(seatId: string): void {
    for (const cell of this.grid.values()) {
      const index = cell.seats.findIndex(seat => seat.id === seatId);
      if (index !== -1) {
        cell.seats.splice(index, 1);
        break;
      }
    }
  }

  /**
   * Remove a section from the spatial index
   */
  removeSection(sectionId: string): void {
    for (const cell of this.grid.values()) {
      const index = cell.sections.findIndex(section => section.id === sectionId);
      if (index !== -1) {
        cell.sections.splice(index, 1);
      }
    }
  }

  /**
   * Get all seats within a viewport
   */
  getSeatsInViewport(viewport: Viewport, padding: number = 0): Seat[] {
    const bounds: SpatialBounds = {
      minX: viewport.x - padding,
      minY: viewport.y - padding,
      maxX: viewport.x + viewport.width + padding,
      maxY: viewport.y + viewport.height + padding
    };

    const seats: Seat[] = [];
    const cellKeys = this.getCellsForBounds(bounds);

    cellKeys.forEach(cellKey => {
      const cell = this.grid.get(cellKey);
      if (cell) {
        cell.seats.forEach(seat => {
          if (this.isPointInBounds(seat.x, seat.y, bounds)) {
            seats.push(seat);
          }
        });
      }
    });

    return seats;
  }

  /**
   * Get all sections within a viewport
   */
  getSectionsInViewport(viewport: Viewport, padding: number = 0): SeatSection[] {
    const bounds: SpatialBounds = {
      minX: viewport.x - padding,
      minY: viewport.y - padding,
      maxX: viewport.x + viewport.width + padding,
      maxY: viewport.y + viewport.height + padding
    };

    const sections: Set<SeatSection> = new Set();
    const cellKeys = this.getCellsForBounds(bounds);

    cellKeys.forEach(cellKey => {
      const cell = this.grid.get(cellKey);
      if (cell) {
        cell.sections.forEach(section => {
          if (this.isSectionInBounds(section, bounds)) {
            sections.add(section);
          }
        });
      }
    });

    return Array.from(sections);
  }

  /**
   * Find seat at specific coordinates
   */
  getSeatAtPoint(x: number, y: number, tolerance: number = 10): Seat | null {
    const cellKey = this.getCellKey(x, y);
    const cell = this.grid.get(cellKey);
    
    if (!cell) return null;

    for (const seat of cell.seats) {
      const distance = Math.sqrt(
        Math.pow(seat.x - x, 2) + Math.pow(seat.y - y, 2)
      );
      if (distance <= tolerance) {
        return seat;
      }
    }

    return null;
  }

  /**
   * Clear the entire spatial index
   */
  clear(): void {
    this.grid.clear();
    this.bounds = { minX: 0, minY: 0, maxX: 0, maxY: 0 };
  }

  /**
   * Get performance statistics
   */
  getStats() {
    const totalCells = this.grid.size;
    const totalSeats = Array.from(this.grid.values()).reduce(
      (sum, cell) => sum + cell.seats.length, 0
    );
    const totalSections = Array.from(this.grid.values()).reduce(
      (sum, cell) => sum + cell.sections.length, 0
    );

    return {
      totalCells,
      totalSeats,
      totalSections,
      bounds: this.bounds,
      cellSize: this.cellSize
    };
  }

  // Private helper methods

  private getCellKey(x: number, y: number): string {
    const cellX = Math.floor(x / this.cellSize);
    const cellY = Math.floor(y / this.cellSize);
    return `${cellX},${cellY}`;
  }

  private parseCellKey(cellKey: string): [number, number] {
    const [x, y] = cellKey.split(',').map(Number);
    return [x, y];
  }

  private createCell(x: number, y: number): SpatialIndexCell {
    const cellX = Math.floor(x / this.cellSize);
    const cellY = Math.floor(y / this.cellSize);
    
    return {
      bounds: {
        minX: cellX * this.cellSize,
        minY: cellY * this.cellSize,
        maxX: (cellX + 1) * this.cellSize,
        maxY: (cellY + 1) * this.cellSize
      },
      seats: [],
      sections: []
    };
  }

  private getCellsForBounds(bounds: SpatialBounds): string[] {
    const cellKeys: string[] = [];
    const minCellX = Math.floor(bounds.minX / this.cellSize);
    const minCellY = Math.floor(bounds.minY / this.cellSize);
    const maxCellX = Math.floor(bounds.maxX / this.cellSize);
    const maxCellY = Math.floor(bounds.maxY / this.cellSize);

    for (let x = minCellX; x <= maxCellX; x++) {
      for (let y = minCellY; y <= maxCellY; y++) {
        cellKeys.push(`${x},${y}`);
      }
    }

    return cellKeys;
  }

  private isPointInBounds(x: number, y: number, bounds: SpatialBounds): boolean {
    return x >= bounds.minX && x <= bounds.maxX && 
           y >= bounds.minY && y <= bounds.maxY;
  }

  private isSectionInBounds(section: SeatSection, bounds: SpatialBounds): boolean {
    const sectionBounds = {
      minX: section.x,
      minY: section.y,
      maxX: section.x + section.width,
      maxY: section.y + section.height
    };

    return !(sectionBounds.maxX < bounds.minX || 
             sectionBounds.minX > bounds.maxX ||
             sectionBounds.maxY < bounds.minY || 
             sectionBounds.minY > bounds.maxY);
  }

  private updateBounds(x: number, y: number): void {
    this.bounds.minX = Math.min(this.bounds.minX, x);
    this.bounds.minY = Math.min(this.bounds.minY, y);
    this.bounds.maxX = Math.max(this.bounds.maxX, x);
    this.bounds.maxY = Math.max(this.bounds.maxY, y);
  }
}
